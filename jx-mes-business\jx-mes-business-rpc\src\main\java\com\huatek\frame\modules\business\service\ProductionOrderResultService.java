package com.huatek.frame.modules.business.service;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderResultVO;
import com.huatek.frame.modules.business.service.dto.*;

/**
* @description 工单检验结果Service
* <AUTHOR>
* @date 2025-07-30
**/
public interface ProductionOrderResultService {

	/**
	 * 根据工单查询工单检验结果
	 * @param id
	 * @return
	 */
	ProductionOrderResultVO selectResultByProductionOrder(String id);

	/**
	 * 根据工单查询工单试验数据
	 * @param productionOrderDTO
	 * @return
	 */
    TorchResponse getProductionOrderTestData(ProductionOrderResultDTO productionOrderDTO);
}