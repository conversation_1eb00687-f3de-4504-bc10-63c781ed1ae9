<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.OutsourcingMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.order_id as orderProcessId,
        t.process_id as processId,
        t.outsourcing_number as outsourcingNumber,
		t.outsourcing_department as outsourcingDepartment,
		t.outsourcing_process as outsourcingProcess,
		t.quantity as quantity,
		t.outsourcing_reason as outsourcingReason,
		t.outsourcing_manufacturer as outsourcingManufacturer,
		t.estimated_price as estimatedPrice,
		t.expected_end_time as expectedEndTime,
		t.actual_price as actualPrice,
		t.status as status,
		t.entire_or_process as entireOrProcess,
        t.comment as comment,
        t.attachment as attachment,
		t.application_time as applicationTime,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
        t.codex_torch_applicant as codexTorchApplicant,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>

    <sql id="Product_Order_Info">
        p.work_order_number as workOrderNumber,
        p.order_number as orderNumber,
        p.product as productId
    </sql>

    <sql id="Product_Info">
        pl.evaluation_order_id as evaluationOrderId,
        pl.standard_specification_id as standardSpecificationId,
        pl.product_model as productModel,
        pl.manufacturer as manufacturer,
        pl.product_category as productCategory,
        pl.product_name as productName,
        pl.production_batch as productionBatch,
        pl.test_type as testType
    </sql>

    <select id="selectOutsourcingApplicationPage" parameterType="com.huatek.frame.modules.business.service.dto.OutsourcingPageDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
		select
		<include refid="Base_Column_List" />, <include refid="Product_Order_Info"/>,
        <include refid="Product_Info"/>, ss.specification_number as specificationNumber, c.entrusted_unit as entrustedUnit
			from outsourcing t
            LEFT JOIN production_order p ON t.order_id = p.id
            LEFT JOIN product_list pl ON p.product = pl.id
            LEFT JOIN evaluation_order e ON e.id = pl.evaluation_order_id
            LEFT JOIN standard_specification ss ON pl.standard_specification_id = ss.id
            LEFT JOIN customer_information_management c ON c.id = e.customer_id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and p.work_order_number like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pl.product_category  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="outsourcingNumber != null and outsourcingNumber != ''">
                    and t.outsourcing_number like concat ('%', #{outsourcingNumber} ,'%')
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and p.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and c.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="outsourcingDepartment != null and outsourcingDepartment != ''">
                    and t.outsourcing_department  like concat('%', #{outsourcingDepartment} ,'%')
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="testType != null and testType != ''">
                    and pl.test_type = #{testType}
                </if>
                <if test="applicationTime != null">
                    and t.application_time  = #{applicationTime}
                </if>
                <if test="productModel != null and productModel != ''">
                    and pl.product_model like concat ('%', #{productModel}, '%')
                </if>
            </where>
	</select>

    <select id="selectOutsourcingApplicationList" parameterType="com.huatek.frame.modules.business.service.dto.OutsourcingDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
		select
		<include refid="Base_Column_List" />
			from outsourcing t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and t.product_category  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="testType != null and testType != ''">
                    and t.test_type  like concat('%', #{testType} ,'%')
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and t.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="testBasis != null and testBasis != ''">
                    and t.test_basis  like concat('%', #{testBasis} ,'%')
                </if>
                <if test="outsourcingDepartment != null and outsourcingDepartment != ''">
                    and t.outsourcing_department  like concat('%', #{outsourcingDepartment} ,'%')
                </if>
                <if test="serialNumber0 != null and serialNumber0 != ''">
                    and t.serial_number0  like concat('%', #{serialNumber0} ,'%')
                </if>
                <if test="outsourcingProcess != null and outsourcingProcess != ''">
                    and t.outsourcing_process  like concat('%', #{outsourcingProcess} ,'%')
                </if>
                <if test="quantity != null and quantity != ''">
                    and t.quantity  like concat('%', #{quantity} ,'%')
                </if>
                <if test="outsourcingReason != null and outsourcingReason != ''">
                    and t.outsourcing_reason  like concat('%', #{outsourcingReason} ,'%')
                </if>
                <if test="outsourcingManufacturer != null and outsourcingManufacturer != ''">
                    and t.outsourcing_manufacturer  like concat('%', #{outsourcingManufacturer} ,'%')
                </if>
                <if test="estimatedPrice != null and estimatedPrice != ''">
                    and t.estimated_price  like concat('%', #{estimatedPrice} ,'%')
                </if>
                <if test="expectedEndTime != null">
                    and t.expected_end_time  = #{expectedEndTime}
                </if>
                <if test="actualPrice != null and actualPrice != ''">
                    and t.actual_price  like concat('%', #{actualPrice} ,'%')
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="processed != null and processed != ''">
                    and t.processed  = #{processed}
                </if>
                <if test="applicationTime != null">
                    and t.application_time  = #{applicationTime}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectOutsourcingApplicationListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
		select
		<include refid="Base_Column_List" />
			from outsourcing t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
    <select id="findPendingOutsourcings" resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
        select
        <include refid="Base_Column_List" />, <include refid="Product_Order_Info"/>,
        <include refid="Product_Info"/>, ss.specification_number as specificationNumber, c.entrusted_unit as entrustedUnit
        from outsourcing t
        LEFT JOIN production_order p ON t.order_id = p.id
        LEFT JOIN product_list pl ON p.product = pl.id
        LEFT JOIN evaluation_order e ON e.id = pl.evaluation_order_id
        LEFT JOIN standard_specification ss ON pl.standard_specification_id = ss.id
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
        <where>
            and t.codex_torch_deleted = '0'
            and t.status = '1'
            <if test="workOrderNumber != null and workOrderNumber != ''">
                and p.work_order_number like concat('%', #{workOrderNumber} ,'%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                and pl.product_category  like concat('%', #{productCategory} ,'%')
            </if>
            <if test="outsourcingNumber != null and outsourcingNumber != ''">
                and t.outsourcing_number like concat ('%', #{outsourcingNumber} ,'%')
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                and p.order_number  like concat('%', #{orderNumber} ,'%')
            </if>
            <if test="entrustedUnit != null and entrustedUnit != ''">
                and c.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
            </if>
            <if test="outsourcingDepartment != null and outsourcingDepartment != ''">
                and t.outsourcing_department  like concat('%', #{outsourcingDepartment} ,'%')
            </if>
            <if test="testType != null and testType != ''">
                and pl.test_type = #{testType}
            </if>
            <if test="applicationTime != null">
                and t.application_time  = #{applicationTime}
            </if>
            <if test="productModel != null and productModel != ''">
                and pl.product_model like concat ('%', #{productModel}, '%')
            </if>
        </where>
    </select>
    <select id="findOutsourcingHistorys"
            resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
        select
        <include refid="Base_Column_List" />, <include refid="Product_Order_Info"/>,
        <include refid="Product_Info"/>, ss.specification_number as specificationNumber, c.entrusted_unit as entrustedUnit
        from outsourcing t
        LEFT JOIN production_order p ON t.order_id = p.id
        LEFT JOIN product_list pl ON p.product = pl.id
        LEFT JOIN evaluation_order e ON e.id = pl.evaluation_order_id
        LEFT JOIN standard_specification ss ON pl.standard_specification_id = ss.id
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
        <where>
            and t.codex_torch_deleted = "0"
            and (t.status = "2" or t.status = "3" or t.status = "4")
            <if test="workOrderNumber != null and workOrderNumber != ''">
                and p.work_order_number like concat('%', #{workOrderNumber} ,'%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                and pl.product_category  like concat('%', #{productCategory} ,'%')
            </if>
            <if test="outsourcingNumber != null and outsourcingNumber != ''">
                and t.outsourcing_number like concat ('%', #{outsourcingNumber} ,'%')
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                and p.order_number  like concat('%', #{orderNumber} ,'%')
            </if>
            <if test="entrustedUnit != null and entrustedUnit != ''">
                and c.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
            </if>
            <if test="outsourcingDepartment != null and outsourcingDepartment != ''">
                and t.outsourcing_department  like concat('%', #{outsourcingDepartment} ,'%')
            </if>
            <if test="testType != null and testType != ''">
                and pl.test_type = #{testType}
            </if>
            <if test="applicationTime != null">
                and t.application_time  = #{applicationTime}
            </if>
            <if test="productModel != null and productModel != ''">
                and pl.product_model like concat ('%', #{productModel}, '%')
            </if>
        </where>
    </select>
    <select id="findOutsourcingDetails" resultType="com.huatek.frame.modules.business.domain.vo.OutsourcingVO">
        select
        <include refid="Base_Column_List" />, <include refid="Product_Order_Info"/>,
        <include refid="Product_Info"/>, ss.specification_number as specificationNumber,c.entrusted_unit as entrustedUnit
        from outsourcing t
        LEFT JOIN production_order p ON t.order_id = p.id
        LEFT JOIN product_list pl ON p.product = pl.id
        LEFT JOIN evaluation_order e ON e.id = pl.evaluation_order_id
        LEFT JOIN standard_specification ss ON pl.standard_specification_id = ss.id
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
        where t.id = #{id}
    </select>
</mapper>