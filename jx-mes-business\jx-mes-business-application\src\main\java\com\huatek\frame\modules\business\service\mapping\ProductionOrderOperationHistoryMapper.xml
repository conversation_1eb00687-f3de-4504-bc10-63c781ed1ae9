<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ProductionOrderOperationHistoryMapper">
	<sql id="Base_Column_List">
		t.id ,
        t.production_order as productionOrder,
        t.operate,
        t.reason,
        t.remark,
        t.operator,
        t.operate_time as operateTime
	</sql>

    <select id="selectProductionOrderOrderOperationHistoryList" parameterType="com.huatek.frame.modules.business.service.dto.ProductionOrderOperationHistoryDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderOperationHistoryVO">
		select
		<include refid="Base_Column_List" />,
        from production_order_operation_history t
            <where>
                <if test="productionOrder != null and productionOrder != ''">
                    and t.production_order  = #{productionOrder}
                </if>
                <if test="operate != null and operate != ''">
                    and t.operate  = #{operate}
                </if>
                <if test="operator != null and operator != ''">
                    and t.operator  = #{operator}
                </if>
                ${params.dataScope}
            </where>
	</select>

	<select id="selectByProductOrder"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionOrderOperationHistoryVO">
        select su.user_name as operator ,p.operate_time as operateTime, p.operate, p.reason ,p.remark
        from production_order_operation_history  p
        left join sys_user su on p.operator =su.id
        where p.production_order=#{id}
    </select>


</mapper>