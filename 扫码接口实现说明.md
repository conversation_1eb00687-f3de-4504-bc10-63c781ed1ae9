# 扫码接口和业务实现说明

## 功能概述
实现了扫码接口和相关业务逻辑，支持通过扫码枪编号和工单编号进行生产任务的状态管理。

## 实现的功能

### 1. 扫码接口
- **接口路径**: `/api/productionTask/scanCode`
- **请求方法**: POST
- **权限**: `productionTask:scanCode`

### 2. 业务逻辑流程

#### 2.1 输入参数验证
- 扫码枪编号（scannerGunNumber）：必填
- 工单编号（workOrderNumber）：必填

#### 2.2 核心业务流程
1. **根据扫码枪编号查询工作站信息**
   - 调用 `WorkstationService.findWorkstationByScannerGunNumber()`
   - 如果未找到工作站，抛出异常

2. **用工作站和工单编号查询ProductionTask信息**
   - 使用工作站ID和工单编号作为查询条件
   - 查询对应的生产任务信息

3. **检查ProductionTask状态**
   - 若任务已经取消（状态=5）或暂停（状态=4），提醒无法开始任务
   - 返回报工页面操作类型

4. **根据任务状态执行不同逻辑**

   **状态为未开始（状态=0）时：**
   - 检查上一步工序是否已完成
   - 检查关联工单前置工序是否已完成
   - 如果前置条件满足：将状态修改为进行中（状态=1），返回开始任务操作类型
   - 如果前置条件不满足：返回报工页面操作类型，提示前置工序未完成

   **其他状态时：**
   - 扫描条码，不修改状态
   - 弹出报工页面，任务状态默认进行中

### 3. 返回结果
返回 `ScanCodeResponseVO` 对象，包含：
- `actionType`: 操作类型（START_TASK 或 SHOW_REPORT_PAGE）
- `message`: 提示消息
- `productionTask`: 生产任务信息
- `workstation`: 工作站信息

## 新增的文件

### 1. DTO类
- `ScanCodeDTO.java`: 扫码请求参数
  - scannerGunNumber: 扫码枪编号
  - workOrderNumber: 工单编号

### 2. VO类
- `ScanCodeResponseVO.java`: 扫码响应结果
  - actionType: 操作类型
  - message: 消息提示
  - productionTask: 生产任务信息
  - workstation: 工作站信息

## 修改的文件

### 1. Service接口
- `ProductionTaskService.java`: 添加扫码方法
- `WorkstationService.java`: 添加根据扫码枪编号查询工作站方法
- `AwaitingProductionOrderService.java`: 添加根据工单编号查询工单方法

### 2. Service实现类
- `ProductionTaskServiceImpl.java`: 实现扫码业务逻辑
- `WorkstationServiceImpl.java`: 实现根据扫码枪编号查询工作站
- `AwaitingProductionOrderServiceImpl.java`: 实现根据工单编号查询工单

### 3. Mapper接口和XML
- `AwaitingProductionOrderMapper.java`: 添加查询方法
- `AwaitingProductionOrderMapper.xml`: 添加SQL查询语句

### 4. Controller
- `ProductionTaskController.java`: 添加扫码接口端点

## 状态常量说明
生产任务状态常量（DicConstant.ProductionOrder）：
- `PRODUCTION_TASK_STATUS_WEIKAISHI = "0"`: 未开始
- `PRODUCTION_TASK_STATUS_JINXINGZHONG = "1"`: 进行中
- `PRODUCTION_TASK_STATUS_ZANTING = "4"`: 暂停
- `PRODUCTION_TASK_STATUS_QUXIAO = "5"`: 取消
- `PRODUCTION_TASK_STATUS_WANCHENG = "6"`: 完成

## 操作类型常量
- `ACTION_TYPE_START_TASK = "START_TASK"`: 开始任务
- `ACTION_TYPE_SHOW_REPORT_PAGE = "SHOW_REPORT_PAGE"`: 显示报工页面

## 使用示例

### 请求示例
```json
{
  "scannerGunNumber": "SCAN001",
  "workOrderNumber": "WO202501001"
}
```

### 响应示例
```json
{
  "status": 200,
  "data": {
    "actionType": "START_TASK",
    "message": "任务已开始",
    "productionTask": {
      "id": "task123",
      "taskNumber": "TASK001",
      "workOrderNumber": "WO202501001",
      "status": "1",
      // ... 其他任务信息
    },
    "workstation": {
      "id": "ws001",
      "workstationName": "工作站1",
      "scannerGunNumber": "SCAN001",
      // ... 其他工作站信息
    }
  }
}
```
