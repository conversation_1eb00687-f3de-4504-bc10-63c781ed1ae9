package com.huatek.frame.modules.business.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description 提交任务DTO
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@ApiModel("提交任务DTO")
public class SubmitTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @ApiModelProperty("任务ID")
    private String id;
}
