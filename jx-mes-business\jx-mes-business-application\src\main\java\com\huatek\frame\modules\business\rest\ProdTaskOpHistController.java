package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.ProdTaskOpHistVO;
import com.huatek.frame.modules.business.service.ProdTaskOpHistService;
import com.huatek.frame.modules.business.service.dto.ProdTaskOpHistDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-11
**/
@Api(tags = "生产任务操作历史管理")
@RestController
@RequestMapping("/api/prodTaskOpHist")
public class ProdTaskOpHistController {
//
//	@Autowired
//    private ProdTaskOpHistService prodTaskOpHistService;
//
//	/**
//	 * 生产任务操作历史列表
//	 *
//	 * @param dto 生产任务操作历史DTO 实体对象
//	 * @return
//	 */
//    @Log("生产任务操作历史列表")
//    @ApiOperation(value = "生产任务操作历史列表查询")
//    @PostMapping(value = "/prodTaskOpHistList", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("prodTaskOpHist:list")
//    public TorchResponse<List<ProdTaskOpHistVO>> query(@RequestBody ProdTaskOpHistDTO dto){
//        return prodTaskOpHistService.findProdTaskOpHistPage(dto);
//    }
//
//	/**
//	 * 新增/修改生产任务操作历史
//	 *
//	 * @param prodTaskOpHistDto 生产任务操作历史DTO实体对象
//	 * @return
//	 * @throws Exception
//	 */
//    @SuppressWarnings("rawtypes")
//    @Log("新增/修改生产任务操作历史")
//    @ApiOperation(value = "生产任务操作历史新增/修改操作")
//    @PostMapping(value = "/prodTaskOpHist", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("prodTaskOpHist:add#prodTaskOpHist:edit")
//    public TorchResponse add(@RequestBody ProdTaskOpHistDTO prodTaskOpHistDto) throws Exception {
//		// BeanValidatorFactory.validate(prodTaskOpHistDto);
//		return prodTaskOpHistService.saveOrUpdate(prodTaskOpHistDto);
//	}
//
//	/**
//	 * 查询生产任务操作历史详情
//	 *
//	 * @param id 主键id
//	 * @return
//	 */
//	@SuppressWarnings({ "rawtypes", "unchecked" })
//    @Log("生产任务操作历史详情")
//    @ApiOperation(value = "生产任务操作历史详情查询")
//    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("prodTaskOpHist:detail")
//	public TorchResponse detail(@PathVariable(value = "id") String id) {
//		return prodTaskOpHistService.findProdTaskOpHist(id);
//	}
//
//	/**
//	 * 删除生产任务操作历史
//	 *
//	 * @param ids
//	 * @return
//	 */
//	@SuppressWarnings("rawtypes")
//    @Log("删除生产任务操作历史")
//    @ApiOperation(value = "生产任务操作历史删除操作")
//    @TorchPerm("prodTaskOpHist:del")
//    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
//	public TorchResponse delete(@RequestBody String[] ids) {
//		return prodTaskOpHistService.delete(ids);
//	}
//
//    @ApiOperation(value = "生产任务操作历史联动选项值查询")
//    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
//	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
//		return prodTaskOpHistService.getOptionsList(id);
//	}
//
//
//
//
//
//    @Log("生产任务操作历史导出")
//    @ApiOperation(value = "生产任务操作历史导出")
//    @TorchPerm("prodTaskOpHist:export")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, @RequestBody ProdTaskOpHistDTO dto)
//    {
//        List<ProdTaskOpHistVO> list = prodTaskOpHistService.selectProdTaskOpHistList(dto);
//        ExcelUtil<ProdTaskOpHistVO> util = new ExcelUtil<ProdTaskOpHistVO>(ProdTaskOpHistVO.class);
//        util.exportExcel(response, list, "生产任务操作历史数据");
//    }
//
//    @Log("生产任务操作历史导入")
//    @ApiOperation(value = "生产任务操作历史导入")
//    @TorchPerm("prodTaskOpHist:import")
//    @PostMapping("/importData")
//    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
//    {
//        ExcelUtil<ProdTaskOpHistVO> util = new ExcelUtil<ProdTaskOpHistVO>(ProdTaskOpHistVO.class);
//        List<ProdTaskOpHistVO> list = util.importExcel(file.getInputStream());
//        return prodTaskOpHistService.importProdTaskOpHist(list, unionColumns, true, "");
//    }
//
//    @Log("生产任务操作历史导入模板")
//    @ApiOperation(value = "生产任务操作历史导入模板下载")
//    @PostMapping("/importTemplate")
//    public void importTemplate(HttpServletResponse response) throws IOException
//    {
//        ExcelUtil<ProdTaskOpHistVO> util = new ExcelUtil<ProdTaskOpHistVO>(ProdTaskOpHistVO.class);
//        util.importTemplateExcel(response, "生产任务操作历史数据");
//    }
//
//    @Log("根据Ids获取生产任务操作历史列表")
//    @ApiOperation(value = "生产任务操作历史 根据Ids批量查询")
//    @PostMapping(value = "/prodTaskOpHistList/ids", produces = {"application/json;charset=utf-8"})
//    public TorchResponse getProdTaskOpHistListByIds(@RequestBody List<String> ids) {
//        return prodTaskOpHistService.selectProdTaskOpHistListByIds(ids);
//    }


}