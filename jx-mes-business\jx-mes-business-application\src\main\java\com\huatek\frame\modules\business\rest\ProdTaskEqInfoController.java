package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.ProdTaskEqInfoVO;
import com.huatek.frame.modules.business.service.ProdTaskEqInfoService;
import com.huatek.frame.modules.business.service.dto.ProdTaskEqInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-11
**/
@Api(tags = "生产任务设备信息管理")
@RestController
@RequestMapping("/api/prodTaskEqInfo")
public class ProdTaskEqInfoController {
//
//	@Autowired
//    private ProdTaskEqInfoService prodTaskEqInfoService;
//
//	/**
//	 * 生产任务设备信息列表
//	 *
//	 * @param dto 生产任务设备信息DTO 实体对象
//	 * @return
//	 */
//    @Log("生产任务设备信息列表")
//    @ApiOperation(value = "生产任务设备信息列表查询")
//    @PostMapping(value = "/prodTaskEqInfoList", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("prodTaskEqInfo:list")
//    public TorchResponse<List<ProdTaskEqInfoVO>> query(@RequestBody ProdTaskEqInfoDTO dto){
//        return prodTaskEqInfoService.findProdTaskEqInfoPage(dto);
//    }
//
//	/**
//	 * 新增/修改生产任务设备信息
//	 *
//	 * @param prodTaskEqInfoDto 生产任务设备信息DTO实体对象
//	 * @return
//	 * @throws Exception
//	 */
//    @SuppressWarnings("rawtypes")
//    @Log("新增/修改生产任务设备信息")
//    @ApiOperation(value = "生产任务设备信息新增/修改操作")
//    @PostMapping(value = "/prodTaskEqInfo", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("prodTaskEqInfo:add#prodTaskEqInfo:edit")
//    public TorchResponse add(@RequestBody ProdTaskEqInfoDTO prodTaskEqInfoDto) throws Exception {
//		// BeanValidatorFactory.validate(prodTaskEqInfoDto);
//		return prodTaskEqInfoService.saveOrUpdate(prodTaskEqInfoDto);
//	}
//
//	/**
//	 * 查询生产任务设备信息详情
//	 *
//	 * @param id 主键id
//	 * @return
//	 */
//	@SuppressWarnings({ "rawtypes", "unchecked" })
//    @Log("生产任务设备信息详情")
//    @ApiOperation(value = "生产任务设备信息详情查询")
//    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("prodTaskEqInfo:detail")
//	public TorchResponse detail(@PathVariable(value = "id") String id) {
//		return prodTaskEqInfoService.findProdTaskEqInfo(id);
//	}
//
//	/**
//	 * 删除生产任务设备信息
//	 *
//	 * @param ids
//	 * @return
//	 */
//	@SuppressWarnings("rawtypes")
//    @Log("删除生产任务设备信息")
//    @ApiOperation(value = "生产任务设备信息删除操作")
//    @TorchPerm("prodTaskEqInfo:del")
//    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
//	public TorchResponse delete(@RequestBody String[] ids) {
//		return prodTaskEqInfoService.delete(ids);
//	}
//
//    @ApiOperation(value = "生产任务设备信息联动选项值查询")
//    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
//	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
//		return prodTaskEqInfoService.getOptionsList(id);
//	}
//
//
//
//
//
//    @Log("生产任务设备信息导出")
//    @ApiOperation(value = "生产任务设备信息导出")
//    @TorchPerm("prodTaskEqInfo:export")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, @RequestBody ProdTaskEqInfoDTO dto)
//    {
//        List<ProdTaskEqInfoVO> list = prodTaskEqInfoService.selectProdTaskEqInfoList(dto);
//        ExcelUtil<ProdTaskEqInfoVO> util = new ExcelUtil<ProdTaskEqInfoVO>(ProdTaskEqInfoVO.class);
//        util.exportExcel(response, list, "生产任务设备信息数据");
//    }
//
//    @Log("生产任务设备信息导入")
//    @ApiOperation(value = "生产任务设备信息导入")
//    @TorchPerm("prodTaskEqInfo:import")
//    @PostMapping("/importData")
//    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
//    {
//        ExcelUtil<ProdTaskEqInfoVO> util = new ExcelUtil<ProdTaskEqInfoVO>(ProdTaskEqInfoVO.class);
//        List<ProdTaskEqInfoVO> list = util.importExcel(file.getInputStream());
//        return prodTaskEqInfoService.importProdTaskEqInfo(list, unionColumns, true, "");
//    }
//
//    @Log("生产任务设备信息导入模板")
//    @ApiOperation(value = "生产任务设备信息导入模板下载")
//    @PostMapping("/importTemplate")
//    public void importTemplate(HttpServletResponse response) throws IOException
//    {
//        ExcelUtil<ProdTaskEqInfoVO> util = new ExcelUtil<ProdTaskEqInfoVO>(ProdTaskEqInfoVO.class);
//        util.importTemplateExcel(response, "生产任务设备信息数据");
//    }
//
//    @Log("根据Ids获取生产任务设备信息列表")
//    @ApiOperation(value = "生产任务设备信息 根据Ids批量查询")
//    @PostMapping(value = "/prodTaskEqInfoList/ids", produces = {"application/json;charset=utf-8"})
//    public TorchResponse getProdTaskEqInfoListByIds(@RequestBody List<String> ids) {
//        return prodTaskEqInfoService.selectProdTaskEqInfoListByIds(ids);
//    }


}