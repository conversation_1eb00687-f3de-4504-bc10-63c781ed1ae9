package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.business.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 封装订单DTO 实体类
* <AUTHOR>
* @date 2025-07-22
**/
@Data
@ApiModel("封装订单DTO实体类")
public class PackageOrderDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    private String orderNumber;
    
    /**
	 * 订单类型
     **/
    @ApiModelProperty("订单类型")
    private String orderType;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String productModel;

    /**
	 * 客户编号
     **/
    @ApiModelProperty("客户编号")
    private String customerId0;

    /**
     * 委托单位
     */
    @ApiModelProperty("委托单位")
    private String entrustedUnit;

    /**
	 * 结算单位
     **/
    @ApiModelProperty("结算单位")
    private String settlementUnit;
    
    /**
	 * 订单状态
     **/
    @ApiModelProperty("订单状态")
    private String orderStatus;
    
    /**
	 * 委托人
     **/
    @ApiModelProperty("委托人")
    private String principal;
    
    /**
	 * 委托人电话
     **/
    @ApiModelProperty("委托人电话")
    private String principalsPhoneNumber;
    
    /**
	 * 接收人
     **/
    @ApiModelProperty("接收人")
    private String recipient;
    
    /**
	 * 委托日期
     **/
    @ApiModelProperty("委托日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfEntrustment;

    /**
     * 查询开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDatetime;

    /**
     * 查询结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDatetime;

    /**
	 * 紧急程度
     **/
    @ApiModelProperty("紧急程度")
    private String urgencyLevel;
    
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    private String comment;
    
    /**
	 * 子表明细项ID
     **/
    @ApiModelProperty("子表明细项ID")
    private String codexTorchDetailItemIds;

    /**
     * 创建人
     **/
    @ApiModelProperty("创建人")
    private String creator;
    
    /**
	 * 创建人id
     **/
    @ApiModelProperty("创建人id")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;



    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


    /**
     * 子表明细项
     */
    @ApiModelProperty("子表明细项")
    private ProductInformationDTO[] detailFormItems;

	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;

}