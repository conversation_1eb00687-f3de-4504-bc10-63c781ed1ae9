package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.annotation.datascope.DataScope;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.business.domain.CapabilityAsset;
import com.huatek.frame.modules.business.domain.CapabilityDevelopment;
import com.huatek.frame.modules.business.domain.ProductManagement;
import com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderResultVO;
import com.huatek.frame.modules.business.mapper.CapabilityAssetMapper;
import com.huatek.frame.modules.business.mapper.CapabilityDevelopmentMapper;
import com.huatek.frame.modules.business.mapper.ProductManagementMapper;
import com.huatek.frame.modules.business.mapper.ProductionOrderResultMapper;
import com.huatek.frame.modules.business.service.CapabilityAssetService;
import com.huatek.frame.modules.business.service.ProductionOrderResultService;
import com.huatek.frame.modules.business.service.dto.CapabilityAssetDTO;
import com.huatek.frame.modules.business.service.dto.CapabilityVerificationCheckDTO;
import com.huatek.frame.modules.business.service.dto.ProductionOrderResultDTO;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;


/**
 * 工单检验结果 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
@DubboService
@Slf4j
public class PrductionOrderResultServiceImpl implements ProductionOrderResultService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @Autowired
    protected Validator validator;


    @Autowired
    private ProductionOrderResultMapper productionOrderResultMapper;
	public PrductionOrderResultServiceImpl(){

	}

    @Override
    public ProductionOrderResultVO selectResultByProductionOrder(String id) {
        return productionOrderResultMapper.selectByWorkOrder(id);
    }

    @Override
    public TorchResponse getProductionOrderTestData(ProductionOrderResultDTO productionOrderDTO) {

        return null;
    }
}
