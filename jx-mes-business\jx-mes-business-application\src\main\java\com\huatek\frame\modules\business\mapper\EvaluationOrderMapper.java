package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.vo.EvaluationOrderQueryRespVO;
import org.apache.ibatis.annotations.Param;
import java.util.Map;
import com.huatek.frame.modules.business.domain.EvaluationOrder;
import  com.huatek.frame.modules.business.domain.vo.EvaluationOrderVO;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 测评订单mapper
* <AUTHOR>
* @date 2025-07-30
**/
public interface EvaluationOrderMapper extends BaseMapper<EvaluationOrder> {

     /**
	 * 测评订单分页
	 * @param dto
	 * @return
	 */
	List<EvaluationOrderVO> selectEvaluationOrderPage(EvaluationOrderDTO dto);

    /**
	 * 外键关联表: customer_information_management - customer_id0
     **/
    @ApiModelProperty("外键 customer_information_management - customer_id0")
	Page<SelectOptionsVO> selectOptionsByEntrustedUnit(String entrustedUnit);
    Map<String,String> selectDataLinkageByEntrustedUnit(@Param("customer_id0") String customer_id0);

    /**
     * 根据条件查询测评订单列表
     *
     * @param dto 测评订单信息
     * @return 测评订单集合信息
     */
    List<EvaluationOrderVO> selectEvaluationOrderList(EvaluationOrderDTO dto);

	/**
	 * 根据IDS查询测评订单列表
	 * @param ids
	 * @return
	 */
    List<EvaluationOrderVO> selectEvaluationOrderListByIds(@Param("ids") List<String> ids);


	/**
	 * 获取测评订单详情
	 * @param id
	 * @return
	 */
    EvaluationOrderVO findEvaluationOrderdetail(@Param("id") String id);
}