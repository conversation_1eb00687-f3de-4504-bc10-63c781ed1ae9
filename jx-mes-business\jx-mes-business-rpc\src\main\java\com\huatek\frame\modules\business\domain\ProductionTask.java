package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
* @description 生产任务
* <AUTHOR>
* @date 2025-08-11
**/
@Setter
@Getter
@TableName("production_task")
public class ProductionTask implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 任务编号
     **/
    @TableField(value = "task_number"
    )
    private String taskNumber;

    
    /**
	 * 工单编号
     **/
    @TableField(value = "work_order_number"
    )
    private String workOrderNumber;

    /**
	 * 执行顺序
     **/
    @TableField(value = "execution_sequence")
    private Integer executionSequence;

    /**
	 * 关联工单前置工序
     **/
    @TableField(value = "asso_wo_pred_proc")
    private String assoWoPredProc;

    /**
	 * 关联工单
     **/
    @TableField(value = "related_work_order")
    private String relatedWorkOrder;

    /**
	 * 工作站
     **/
    @TableField(value = "workstation")
    private String workstation;

    
    /**
	 * 送检数量
     **/
    @TableField(value = "inspection_quantity2"
    )
    private Integer inspectionQuantity2;

    
    /**
	 * 工序名称
     **/
    @TableField(value = "process_name2"
    )
    private String processName2;


    /**
	 * 工序编码
     **/
    @TableField(value = "process_code"
    )
    private String processCode;


    /**
	 * 试验依据
     **/
    @TableField(value = "test_basis"
    )
    private String testBasis;

    
    /**
	 * 试验条件
     **/
    @TableField(value = "test_conditions"
    )
    private String testConditions;

    
    /**
	 * 判定依据
     **/
    @TableField(value = "judgment_criteria"
    )
    private String judgmentCriteria;

    
    /**
	 * 状态
     **/
    @TableField(value = "status"
    )
    private String status;

    
    /**
	 * 技术能力编号
     **/
    @TableField(value = "technical_competency_number"
    )
    private String technicalCompetencyNumber;

    
    /**
	 * 操作卡
     **/
    @TableField(value = "operation_card"
    )
    private String operationCard;

    
    /**
	 * 备注
     **/
    @TableField(value = "`comment`"
    )
    private String comment;

    
    /**
	 * 工单等级
     **/
    @TableField(value = "ticket_level"
    )
    private String ticketLevel;

    
    /**
	 * 计划开始时间
     **/
    @TableField(value = "scheduled_start_time"
    )
    private Timestamp scheduledStartTime;

    
    /**
	 * 计划结束时间
     **/
    @TableField(value = "scheduled_end_time"
    )
    private Timestamp scheduledEndTime;

    
    /**
	 * 产品名称
     **/
    @TableField(value = "product_name"
    )
    private String productName;

    
    /**
	 * 产品型号
     **/
    @TableField(value = "product_model"
    )
    private String productModel;

    
    /**
	 * 生产厂家
     **/
    @TableField(value = "manufacturer"
    )
    private String manufacturer;

    
    /**
	 * 批次号
     **/
    @TableField(value = "batch_number"
    )
    private String batchNumber;

    
    /**
	 * 产品分类
     **/
    @TableField(value = "product_category"
    )
    private String productCategory;

    
    /**
	 * 产品资料
     **/
    @TableField(value = "product_information1"
    )
    private String productInformation1;

    
    /**
	 * 委托单位
     **/
    @TableField(value = "entrusted_unit"
    )
    private String entrustedUnit;

    
    /**
	 * 组别
     **/
    @TableField(value = "`grouping`"
    )
    private String grouping;

    
    /**
	 * 试验方式
     **/
    @TableField(value = "test_methodology"
    )
    private String testMethodology;

    
    /**
	 * 实际开始时间
     **/
    @TableField(value = "actual_start_time"
    )
    private Timestamp actualStartTime;

    
    /**
	 * 实际结束时间
     **/
    @TableField(value = "actual_end_time"
    )
    private Timestamp actualEndTime;

    
    /**
	 * 试验类型
     **/
    @TableField(value = "test_type"
    )
    private String testType;

    
    /**
	 * 所属部门
     **/
    @TableField(value = "department"
    )
    private String department;

    
    /**
	 * 所属班组
     **/
    @TableField(value = "belonging_team2"
    )
    private String belongingTeam2;

    
    /**
	 * 报工时间
     **/
    @TableField(value = "reporting_time0"
    )
    private Timestamp reportingTime0;

    
    /**
	 * PDA预警
     **/
    @TableField(value = "pda_warning"
    )
    private String pdaWarning;

    
    /**
	 * 报工人
     **/
    @TableField(value = "reporter4"
    )
    private String reporter4;

    
    /**
	 * 不合格编号
     **/
    @TableField(value = "non_conformity_number"
    )
    private String nonConformityNumber;

    
    /**
	 * 合格数量
     **/
    @TableField(value = "qualified_quantity"
    )
    private Integer qualifiedQuantity;

    
    /**
	 * 不合格数量
     **/
    @TableField(value = "unqualified_quantity"
    )
    private Integer unqualifiedQuantity;

    
    /**
	 * 失效模式
     **/
    @TableField(value = "failure_mode"
    )
    private String failureMode;

    
    /**
	 * 完成时间
     **/
    @TableField(value = "completion_time6"
    )
    private Date completionTime6;

    
    /**
	 * PDA
     **/
    @TableField(value = "pda"
    )
    private BigDecimal pda;

    
    /**
	 * 记录更改盖章
     **/
    @TableField(value = "record_change_stamp"
    )
    private Integer recordChangeStamp;

    
    /**
	 * 湿度
     **/
    @TableField(value = "humidity"
    )
    private String humidity;

    
    /**
	 * 温度
     **/
    @TableField(value = "temperature"
    )
    private String temperature;

    
    /**
	 * 试验结果总结
     **/
    @TableField(value = "test_result_summary"
    )
    private String testResultSummary;

    
    /**
	 * 报工备注
     **/
    @TableField(value = "report_work_remarks"
    )
    private String reportWorkRemarks;

    
    /**
	 * 附件
     **/
    @TableField(value = "attachment"
    )
    private String attachment;

    /**
	 * 关联异常反馈编号
     **/
    @TableField(value = "assoc_exception_feedback_num"
    )
    private String assocExceptionFeedbackNum;

    /**
	 * 暂停原因
     **/
    @TableField(value = "pause_reason")
    private String pauseReason;

    /**
     * 已完成数量
     **/
    @TableField(value = "completed_quantity")
    private Integer completedQuantity;

    /**
     * 客户工序名称
     **/
    @TableField(value = "customer_process_name")
    private String customerProcessName;

    /**
     * 显示序号
     **/
    @TableField(value = "display_number")
    private Integer displayNumber;

    /**
     * 负责人
     **/
    @TableField(value = "responsible_person")
    private String responsiblePerson;

    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}