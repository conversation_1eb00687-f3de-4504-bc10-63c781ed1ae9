package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;
import java.io.Serializable;
import java.util.List;

/**
* @description 生产任务DTO 实体类
* <AUTHOR>
* @date 2025-08-11
**/
@Data
@ApiModel("生产任务DTO实体类")
public class ProductionTaskDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 任务编号
     **/
    @ApiModelProperty("任务编号")
    private String taskNumber;
    
    /**
	 * 工单编号
     **/
    @ApiModelProperty("工单编号")
    private String workOrderNumber;

    /**
	 * 执行顺序
     **/
    @ApiModelProperty("执行顺序")
    private Integer executionSequence;

    /**
	 * 关联工单前置工序
     **/
    @ApiModelProperty("关联工单前置工序")
    private String assoWoPredProc;

    /**
	 * 关联工单
     **/
    @ApiModelProperty("关联工单")
    private String relatedWorkOrder;

    /**
	 * 工作站
     **/
    @ApiModelProperty("工作站")
    private String workstation;
    
    /**
	 * 送检数量
     **/
    @ApiModelProperty("送检数量")
    private Integer inspectionQuantity2;
    
    /**
	 * 工序名称
     **/
    @ApiModelProperty("工序名称")
    private String processName2;

    /**
	 * 工序编码
     **/
    @ApiModelProperty("工序编码")
    private String processCode;

    /**
	 * 试验依据
     **/
    @ApiModelProperty("试验依据")
    private String testBasis;
    
    /**
	 * 试验条件
     **/
    @ApiModelProperty("试验条件")
    private String testConditions;
    
    /**
	 * 判定依据
     **/
    @ApiModelProperty("判定依据")
    private String judgmentCriteria;
    
    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    private String status;
    
    /**
	 * 技术能力编号
     **/
    @ApiModelProperty("技术能力编号")
    private String technicalCompetencyNumber;
    
    /**
	 * 操作卡
     **/
    @ApiModelProperty("操作卡")
    private String operationCard;
    
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    private String comment;
    
    /**
	 * 工单等级
     **/
    @ApiModelProperty("工单等级")
    private String ticketLevel;
    
    /**
	 * 计划开始时间
     **/
    @ApiModelProperty("计划开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp scheduledStartTime;
    
    /**
	 * 计划结束时间
     **/
    @ApiModelProperty("计划结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp scheduledEndTime;
    
    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    private String productName;
    
    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    private String productModel;
    
    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    private String manufacturer;
    
    /**
	 * 批次号
     **/
    @ApiModelProperty("批次号")
    private String batchNumber;
    
    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    private String productCategory;
    
    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
    private String productInformation1;
    
    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    private String entrustedUnit;
    
    /**
	 * 组别
     **/
    @ApiModelProperty("组别")
    private String grouping;
    
    /**
	 * 试验方式
     **/
    @ApiModelProperty("试验方式")
    private String testMethodology;
    
    /**
	 * 实际开始时间
     **/
    @ApiModelProperty("实际开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp actualStartTime;
    
    /**
	 * 实际结束时间
     **/
    @ApiModelProperty("实际结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp actualEndTime;
    
    /**
	 * 试验类型
     **/
    @ApiModelProperty("试验类型")
    private String testType;
    
    /**
	 * 所属部门
     **/
    @ApiModelProperty("所属部门")
    private String department;
    
    /**
	 * 所属班组
     **/
    @ApiModelProperty("所属班组")
    private String belongingTeam2;
    
    /**
	 * 报工时间
     **/
    @ApiModelProperty("报工时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp reportingTime0;
    
    /**
	 * PDA预警
     **/
    @ApiModelProperty("PDA预警")
    private String pdaWarning;
    
    /**
	 * 报工人
     **/
    @ApiModelProperty("报工人")
    private String reporter4;
    
    /**
	 * 不合格编号
     **/
    @ApiModelProperty("不合格编号")
    private String nonConformityNumber;
    
    /**
	 * 合格数量
     **/
    @ApiModelProperty("合格数量")
    private Integer qualifiedQuantity;
    
    /**
	 * 不合格数量
     **/
    @ApiModelProperty("不合格数量")
    private Integer unqualifiedQuantity;
    
    /**
	 * 失效模式
     **/
    @ApiModelProperty("失效模式")
    private String failureMode;
    
    /**
	 * 完成时间
     **/
    @ApiModelProperty("完成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completionTime6;
    
    /**
	 * PDA
     **/
    @ApiModelProperty("PDA")
    private BigDecimal pda;
    
    /**
	 * 记录更改盖章
     **/
    @ApiModelProperty("记录更改盖章")
    private Long recordChangeStamp;
    
    /**
	 * 湿度
     **/
    @ApiModelProperty("湿度")
    private String humidity;
    
    /**
	 * 温度
     **/
    @ApiModelProperty("温度")
    private String temperature;
    
    /**
	 * 试验结果总结
     **/
    @ApiModelProperty("试验结果总结")
    private String testResultSummary;
    
    /**
	 * 报工备注
     **/
    @ApiModelProperty("报工备注")
    private String reportWorkRemarks;
    
    /**
	 * 附件
     **/
    @ApiModelProperty("附件")
    private String attachment;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;



	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;


    @ApiModelProperty("试验数据")
    private List<ProductionTaskTestDataDTO> productionTaskTestDataList;

    @ApiModelProperty("设备信息")
    private List<ProdTaskEqInfoDTO>  prodTaskEqInfoList;

    @ApiModelProperty("附件信息")
    private List<ProductionTaskAttachmentsDTO> prodTaskAttachmentList;


    //    @ApiModelProperty("操作历史")
    //    private List<ProdTaskOpHistDTO> prodTaskOpHistDTOS;

    @ApiModelProperty("关联异常反馈编号")
    private String assocExceptionFeedbackNum;

    @ApiModelProperty("暂停原因")
    private String pauseReason;

    @ApiModelProperty("已完成数量")
    private Integer completedQuantity;

    @ApiModelProperty("客户工序名称")
    private String customerProcessName;

    @ApiModelProperty("显示序号")
    private Integer displayNumber;

    @ApiModelProperty("待办(0)or全部(1)")
    private String toDoOrAll;
    @ApiModelProperty("是否管理员,管理员会忽略角色和班组等权限")
    private Boolean isAdmin;
}