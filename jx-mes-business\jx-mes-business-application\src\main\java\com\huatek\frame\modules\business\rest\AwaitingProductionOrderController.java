package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderVO;
import com.huatek.frame.modules.business.service.AwaitingProductionOrderService;
import com.huatek.frame.modules.business.service.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-07-30
**/
@Api(tags = "待制工单管理")
@RestController
@RequestMapping("/api/awaitingProductionOrder")
public class AwaitingProductionOrderController {

	@Autowired
    private AwaitingProductionOrderService awaitingProductionOrderService;

	/**
	 * 待制工单列表
	 * 
	 * @param dto 待制工单DTO 实体对象
	 * @return
	 */
    @Log("待制工单列表")
    @ApiOperation(value = "待制工单列表查询")
    @PostMapping(value = "/awaitingProductionOrderList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("awaitingProductionOrder:list")
    public TorchResponse<List<ProductionOrderVO>> query(@RequestBody ProductionOrderDTO dto){
        return awaitingProductionOrderService.findAwaitingProductionOrderPage(dto);
    }

	/**
	 * 新增/修改待制工单
	 * 
	 * @param awaitingProductionOrderDto 待制工单DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改待制工单")
    @ApiOperation(value = "待制工单新增/修改操作")
    @PostMapping(value = "/awaitingProductionOrder", produces = { "application/json;charset=utf-8" })
    @TorchPerm("awaitingProductionOrder:add#awaitingProductionOrder:edit")
    public TorchResponse add(@RequestBody ProductionOrderDTO awaitingProductionOrderDto) throws Exception {
		// BeanValidatorFactory.validate(awaitingProductionOrderDto);
		return awaitingProductionOrderService.saveOrUpdate(awaitingProductionOrderDto);
	}

	/**
	 * 查询待制工单详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("待制工单")
    @ApiOperation(value = "待制工单详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("awaitingProductionOrder:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return awaitingProductionOrderService.findAwaitingProductionOrder(id);
	}

    /**
     * 工单查看
     *
     * @param id 主键id
     * @return
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("工单查看")
    @ApiOperation(value = "工单查看")
    @GetMapping(value = "/view/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("awaitingProductionOrder:detail")
    public TorchResponse view(@PathVariable(value = "id") String id) {
        return awaitingProductionOrderService.findAwaitingProductionOrderView(id);
    }

	/**
	 * 删除待制工单
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除待制工单")
    @ApiOperation(value = "待制工单删除操作")
    @TorchPerm("awaitingProductionOrder:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return awaitingProductionOrderService.delete(ids);
	}

    @ApiOperation(value = "待制工单联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return awaitingProductionOrderService.getOptionsList(id);
	}

    /**
     * 待制工单审批
     *
     * @param formApprovalDTO 表单审批DTO实体对象
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @Log("待制工单审批")
    @ApiOperation(value = "待制工单审批")
    @PostMapping(value = "/approval", produces = { "application/json;charset=utf-8" })
    @TorchPerm("awaitingProductionOrder:approval#awaitingProductionOrderApproval:approval")
    public TorchResponse approve(@RequestBody FormApprovalDTO formApprovalDTO, @RequestHeader(value = Constant.TOKEN) String token) throws Exception {
        // BeanValidatorFactory.validate(formApprovalDTO);
        return awaitingProductionOrderService.approve(formApprovalDTO, token);
    }

    /**
     * 提交审批待制工单
     *
     * @param awaitingProductionOrderDto 待制工单DTO实体对象
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @Log("提交审批待制工单")
    @ApiOperation(value = "待制工单提交审批")
    @PostMapping(value = "/apply", produces = { "application/json;charset=utf-8" })
    @TorchPerm("awaitingProductionOrder:add#awaitingProductionOrder:edit")
    public TorchResponse apply(@RequestBody ProductionOrderDTO awaitingProductionOrderDto, @RequestHeader(value = Constant.TOKEN) String token) throws Exception {
        // BeanValidatorFactory.validate(awaitingProductionOrderDto);
        return awaitingProductionOrderService.apply(awaitingProductionOrderDto,token);
    }


    @ApiOperation(value = "待制工单 自动填充数据查询")
    @GetMapping(value = "/linkageData/{linkageDataTableName}/{conditionalValue}", produces = { "application/json;charset=utf-8" })
    public TorchResponse getLinkageData(@PathVariable(value = "linkageDataTableName") String linkageDataTableName,
    									@PathVariable(value = "conditionalValue") String conditionalValue) {
		return awaitingProductionOrderService.getLinkageData(linkageDataTableName, conditionalValue);
	}

    @Log("待制工单导出")
    @ApiOperation(value = "待制工单导出")
    @TorchPerm("awaitingProductionOrder:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody ProductionOrderDTO dto)
    {
        List<ProductionOrderVO> list = awaitingProductionOrderService.selectAwaitingProductionOrderList(dto);
        ExcelUtil<ProductionOrderVO> util = new ExcelUtil<ProductionOrderVO>(ProductionOrderVO.class);
        util.exportExcel(response, list, "待制工单数据");
    }



    @Log("根据Ids获取待制工单列表")
    @ApiOperation(value = "待制工单 根据Ids批量查询")
    @PostMapping(value = "/awaitingProductionOrderList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getAwaitingProductionOrderListByIds(@RequestBody List<String> ids) {
        return awaitingProductionOrderService.selectAwaitingProductionOrderListByIds(ids);
    }

    @Log("测评订单产品下发")
    @ApiOperation(value = "测评订单产品下发")
    @PostMapping(value = "/issued/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse issuedProductByIds(@RequestBody List<String> ids) {
        return awaitingProductionOrderService.issuedProductByIds(ids);
    }

    @Log("指派负责人")
    @ApiOperation(value = "指派负责人")
    @TorchPerm("awaitingProductionOrder:setResponsiblePerson#productionOrder:setResponsiblePerson")
    @PostMapping(value = "/setResponsiblePerson", produces = {"application/json;charset=utf-8"})
    public TorchResponse setResponsiblePerson(@RequestBody ProductionOrderDTO awaitingProductionOrderDto) {
        return awaitingProductionOrderService.setResponsiblePerson(awaitingProductionOrderDto);
    }

    @Log("判断是否有同型号同批次订单")
    @ApiOperation(value = "判断是否有同型号同批次订单")
    @TorchPerm("awaitingProductionOrder:bindPlan")
    @PostMapping(value = "/judgeModelAndBatch", produces = {"application/json;charset=utf-8"})
    public TorchResponse judgeModelAndBatch(@RequestBody ProductionOrderDTO awaitingProductionOrderDto) {
        return awaitingProductionOrderService.judgeModelAndBatch(awaitingProductionOrderDto);
    }


    @Log("设置同型同批次同厂家标记")
    @ApiOperation(value = "设置同型同批次同厂家标记")
    @TorchPerm("awaitingProductionOrder:bindPlan")
    @PostMapping(value = "/saveWtstabr", produces = {"application/json;charset=utf-8"})
    public TorchResponse SaveWtstabr(@RequestBody ProductionOrderDTO awaitingProductionOrderDto) {
        return awaitingProductionOrderService.SaveWtstabr(awaitingProductionOrderDto);
    }

    @Log("保存工单工序方案")
    @ApiOperation(value = "保存工单工序方案")
    @TorchPerm("awaitingProductionOrder:bindPlan")
    @PostMapping(value = "/saveCustomerProcessSheme", produces = {"application/json;charset=utf-8"})
    public TorchResponse saveCustomerProcessSheme(@RequestBody CustomerProcessSchemeDTO customerProcessSchemeDTO, @RequestHeader(value = Constant.TOKEN) String token) {
        return awaitingProductionOrderService.saveCustomerProcessSheme(customerProcessSchemeDTO,token);
    }

    @Log("根据工序方案查询试验项目")
    @ApiOperation(value = "根据工序方案查询试验项目")
    @TorchPerm("awaitingProductionOrder:bindPlan")
    @PostMapping(value = "/getExperimentProjectByPlan", produces = {"application/json;charset=utf-8"})
    public TorchResponse getExperimentProjectByPlan(@RequestBody ExperimentProjectDTO dxperimentProjectDTO) {
        return awaitingProductionOrderService.getExperimentProjectByPlan(dxperimentProjectDTO);
    }

    @Log("根据客户工序方案查询试验项目")
    @ApiOperation(value = "根据工序方案查询试验项目")
    @TorchPerm("awaitingProductionOrder:bindPlan")
    @PostMapping(value = "/getExperimentProjectByCustomerPlan", produces = {"application/json;charset=utf-8"})
    public TorchResponse getExperimentProjectByCustomerPlan(@RequestBody CustomerExperimentProjectDTO customerExperimentProjectDTO) {
        return awaitingProductionOrderService.getExperimentProjectByCustomerPlan(customerExperimentProjectDTO);
    }

    @Log("根据工序获取试验项目")
    @ApiOperation(value = "根据工序获取试验项目")
    @TorchPerm("awaitingProductionOrder:bindPlan")
    @PostMapping(value = "/getExperimentProjectByProcess", produces = {"application/json;charset=utf-8"})
    public TorchResponse getExperimentProjectByProcess(@RequestBody String[] ids) {
        return awaitingProductionOrderService.getExperimentProjectByProcess(ids);
    }
    @Log("查询工序")
    @ApiOperation(value = "查询工序")
    @TorchPerm("awaitingProductionOrder:bindPlan")
    @PostMapping(value = "/getStandProcessForCustomerPlan", produces = {"application/json;charset=utf-8"})
    public TorchResponse getStandProcessForCustomerPlan(@RequestBody StandardProcessManagementDTO standardProcessManagementDTO) {
        return awaitingProductionOrderService.getStandProcessForCustomerPlan(standardProcessManagementDTO);
    }

    @Log("查询工单绑定方案")
    @ApiOperation(value = "查询工单绑定方案")
    @TorchPerm("awaitingProductionOrder:bindPlan")
    @PostMapping(value = "/getBindProcessSchemePlan", produces = {"application/json;charset=utf-8"})
    public TorchResponse getBindProcessSchemePlan(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.getBindProcessSchemePlan(productionOrderDTO);
    }
    @Log("复制工单")
    @ApiOperation(value = "复制工单")
    @TorchPerm("productionOrder:copy")
    @PostMapping(value = "/copyProductionOrder", produces = {"application/json;charset=utf-8"})
    public TorchResponse copyProductionOrder(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.copyProductionOrder(productionOrderDTO);
    }
    @Log("分单")
    @ApiOperation(value = "分单")
    @TorchPerm("productionOrder:split")
    @PostMapping(value = "/splitProductionOrder", produces = {"application/json;charset=utf-8"})
    public TorchResponse splitProductionOrder(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.splitProductionOrder(productionOrderDTO);
    }

    @Log("消除pda预警")
    @ApiOperation(value = "消除pda预警")
    @TorchPerm("productionOrder:cancelPdaWarning")
    @PostMapping(value = "/cancelPdaWarning", produces = {"application/json;charset=utf-8"})
    public TorchResponse cancelPdaWarning(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.cancelPdaWarning(productionOrderDTO);
    }
    @Log("暂停")
    @ApiOperation(value = "暂停")
    @TorchPerm("productionOrder:pause")
    @PostMapping(value = "/pauseProductionOrder", produces = {"application/json;charset=utf-8"})
    public TorchResponse pauseProductionOrder(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.pauseProductionOrder(productionOrderDTO);
    }
    @Log("恢复")
    @ApiOperation(value = "恢复")
    @TorchPerm("productionOrder:restore")
    @PostMapping(value = "/restoreProductionOrder", produces = {"application/json;charset=utf-8"})
    public TorchResponse restoreProductionOrder(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.restoreProductionOrder(productionOrderDTO);
    }

    @Log("取消")
    @ApiOperation(value = "取消")
    @TorchPerm("productionOrder:cancel")
    @PostMapping(value = "/cancelProductionOrder", produces = {"application/json;charset=utf-8"})
    public TorchResponse cancelProductionOrder(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.cancelProductionOrder(productionOrderDTO);
    }

    @Log("不可筛")
    @ApiOperation(value = "不可筛")
    @TorchPerm("productionOrder:noScreened")
    @PostMapping(value = "/connotScreened", produces = {"application/json;charset=utf-8"})
    public TorchResponse connotScreened(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.connotScreened(productionOrderDTO);
    }

    @Log("外协申请")
    @ApiOperation(value = "外协申请")
    @TorchPerm("productionOrder:outSourcingApply")
    @PostMapping(value = "/outSourcingApply", produces = {"application/json;charset=utf-8"})
    public TorchResponse outSourcingApply(@RequestBody AddOrUpdateOutsourcingDTO addOrUpdateOutsourcingDTO) {
        return awaitingProductionOrderService.outSourcingApply(addOrUpdateOutsourcingDTO);
    }

    @Log("任务下发")
    @ApiOperation(value = "任务下发")
    @TorchPerm("productionOrder:issueTask")
    @PostMapping(value = "/issueTask", produces = {"application/json;charset=utf-8"})
    public TorchResponse issueProductionOrderTask(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.issueProductionOrderTask(productionOrderDTO);
    }
}