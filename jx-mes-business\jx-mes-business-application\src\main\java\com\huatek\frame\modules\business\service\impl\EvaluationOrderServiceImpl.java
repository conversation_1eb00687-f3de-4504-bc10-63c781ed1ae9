package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.ProductList;
import com.huatek.frame.modules.business.domain.vo.EvaluationOrderQueryRespVO;
import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RBloomFilter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.EvaluationOrder;
import com.huatek.frame.modules.business.domain.vo.EvaluationOrderVO;
import com.huatek.frame.modules.business.mapper.EvaluationOrderMapper;
import com.huatek.frame.modules.business.service.EvaluationOrderService;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderDTO;
import com.huatek.frame.modules.business.domain.CustomerInformationManagement;
import com.huatek.frame.modules.business.mapper.CustomerInformationManagementMapper;
import org.springframework.util.CollectionUtils;

import com.huatek.frame.modules.business.domain.vo.ProductListVO;
import com.huatek.frame.modules.business.service.ProductListService;
import com.huatek.frame.modules.business.service.dto.ProductListDTO;

import static com.huatek.frame.modules.constant.RedisKeyConstant.PRODUCT_INFO_CREATE_KEY;


/**
 * 测评订单 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "evaluationOrder")
//@RefreshScope
@Slf4j
public class EvaluationOrderServiceImpl extends ServiceImpl<EvaluationOrderMapper, EvaluationOrder> implements EvaluationOrderService  {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private EvaluationOrderMapper evaluationOrderMapper;

	@Autowired
    private CustomerInformationManagementMapper customerInformationManagementMapper;

    @Autowired
    protected Validator validator;

    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    private RBloomFilter<String> productInfoCreateCachePenetrationBloomFilter;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();


    @Autowired
    private ProductListService productListService;


	public EvaluationOrderServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<EvaluationOrderVO>> findEvaluationOrderPage(EvaluationOrderDTO dto) {
        if(!HuatekTools.isEmpty(dto.getOrderType())) {
           String evaluationOrder = dto.getOrderType().replaceAll(",", "|");
           evaluationOrder = MATCH_MULTIPLE_VALUE_REGEXP.replace("#", evaluationOrder);
           dto.setOrderType(evaluationOrder);
        }

		PageHelper.startPage(dto.getPage(), dto.getLimit());
//		Page<EvaluationOrderQueryRespVO> evaluationOrders = evaluationOrderMapper.selectEvaluationOrderPage(dto);

        List<EvaluationOrderVO> evaluationOrders = evaluationOrderMapper.selectEvaluationOrderPage(dto);

		TorchResponse<List<EvaluationOrderVO>> response = new TorchResponse<List<EvaluationOrderVO>>();
		response.getData().setData(evaluationOrders);
        response.getData().setCount((long)evaluationOrders.size());
		response.setStatus(200);

		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(EvaluationOrderDTO evaluationOrderDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(evaluationOrderDto.getCodexTorchDeleted())) {
            evaluationOrderDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = evaluationOrderDto.getId();
		EvaluationOrder entity = new EvaluationOrder();
        BeanUtils.copyProperties(evaluationOrderDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
            //自动生成订单编号
            TorchResponse response =  codeManagementService.getOrderNumber("CPDD");
            entity.setOrderNumber(response.getData().getData().toString());
			evaluationOrderMapper.insert(entity);
		} else {
			evaluationOrderMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        EvaluationOrderVO vo = new EvaluationOrderVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<EvaluationOrderVO> findEvaluationOrder(String id) {
		EvaluationOrderVO vo = new EvaluationOrderVO();
		if (!HuatekTools.isEmpty(id)) {
//			EvaluationOrder entity = evaluationOrderMapper.selectById(id);
            EvaluationOrderVO entity = evaluationOrderMapper.findEvaluationOrderdetail(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<EvaluationOrderVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<EvaluationOrder> evaluationOrderList = evaluationOrderMapper.selectBatchIds(Arrays.asList(ids));
        for (EvaluationOrder evaluationOrder : evaluationOrderList) {
            evaluationOrder.setCodexTorchDeleted(Constant.DEFAULT_YES);
            evaluationOrderMapper.updateById(evaluationOrder);
        }
		//evaluationOrderMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("entrustedUnit",evaluationOrderMapper::selectOptionsByEntrustedUnit);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}




    @Override
	public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
	    Map<String, String> data = new HashMap();
        try {
	        switch (linkageDataTableName) {
                case "customer_information_management":
                    data = selectDataLinkageByEntrustedUnit(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
	    TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
	}
    @Override
    public Map<String,String> selectDataLinkageByEntrustedUnit(String customer_id0) {
        return evaluationOrderMapper.selectDataLinkageByEntrustedUnit(customer_id0);
    }

    @Override
    @ExcelExportConversion(tableName = "evaluation_order", convertorFields = "orderType,urgencyLevel,reportRequirements,reportFormat,dataFormat,dataReqERep,dataReqsPapereport,status,productionStage,testMethodology")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<EvaluationOrderVO> selectEvaluationOrderList(EvaluationOrderDTO dto) {
        return evaluationOrderMapper.selectEvaluationOrderList(dto);
    }

    /**
     * 导入测评订单数据
     *
     * @param evaluationOrderList 测评订单数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "evaluation_order", convertorFields = "orderType,urgencyLevel,reportRequirements,reportFormat,dataFormat,dataReqERep,dataReqsPapereport,status,productionStage,testMethodology")
    public TorchResponse importEvaluationOrder(List<EvaluationOrderVO> evaluationOrderList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(evaluationOrderList) || evaluationOrderList.size() == 0) {
            throw new ServiceException("导入测评订单数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (EvaluationOrderVO vo : evaluationOrderList) {
            try {
                EvaluationOrder evaluationOrder = new EvaluationOrder();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, evaluationOrder);
                QueryWrapper<EvaluationOrder> wrapper = new QueryWrapper();
                EvaluationOrder oldEvaluationOrder = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = EvaluationOrderVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<EvaluationOrder> oldEvaluationOrderList = evaluationOrderMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldEvaluationOrderList) && oldEvaluationOrderList.size() > 1) {
                        evaluationOrderMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldEvaluationOrderList) && oldEvaluationOrderList.size() == 1) {
                        oldEvaluationOrder = oldEvaluationOrderList.get(0);
                    }
                }
                if (StringUtils.isNull(oldEvaluationOrder)) {
                    BeanValidators.validateWithException(validator, vo);
                    evaluationOrderMapper.insert(evaluationOrder);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、订单编号 " + vo.getOrderNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldEvaluationOrder, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    evaluationOrderMapper.updateById(oldEvaluationOrder);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、订单编号 " + vo.getOrderNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、订单编号 " + vo.getOrderNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、订单编号 " + vo.getOrderNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(EvaluationOrderVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getOrderType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>订单类型不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getEntrustedUnit())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>委托单位不能为空!");
        }
        if (!HuatekTools.isEmpty(vo.getEntrustedUnit())) {
            List<String> entrustedUnitList = Arrays.asList(vo.getEntrustedUnit().split(","));
            List<CustomerInformationManagement> list = customerInformationManagementMapper.selectList(new QueryWrapper<CustomerInformationManagement>().in("customer_id0", entrustedUnitList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("委托单位=" + vo.getEntrustedUnit() + "; ");
            }
        }
        if (HuatekTools.isEmpty(vo.getSettlementUnit())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>结算单位不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getDateOfEntrustment())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>委托日期不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getDeadline())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>要求完成日期不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getReportRequirements())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>报告需求不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getAcceptanceNotice())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>验收通知单不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getPurchaseOrderContractNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>订货合同号不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectEvaluationOrderListByIds(List<String> ids) {
        List<EvaluationOrderVO> evaluationOrderList = evaluationOrderMapper.selectEvaluationOrderListByIds(ids);

		TorchResponse<List<EvaluationOrderVO>> response = new TorchResponse<List<EvaluationOrderVO>>();
		response.getData().setData(evaluationOrderList);
		response.setStatus(200);
		response.getData().setCount((long)evaluationOrderList.size());
		return response;
    }

    /**
     * 测评订单主子表单组合提交
     *
	 * @param evaluationOrderDto 测评订单DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse submitMasterDetails(EvaluationOrderDTO evaluationOrderDto){
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(evaluationOrderDto.getCodexTorchDeleted())) {
            evaluationOrderDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }

        //非必要字段处理
        evaluationOrderDto.setId("");
        evaluationOrderDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        evaluationOrderDto.setCustomerId(evaluationOrderDto.getEntrustedUnit());
        //测评订单状态为未开始
        evaluationOrderDto.setProductionStage(DicConstant.SalesOrder.EVALUATION_ORDER_STATUS_NOT_STARTED);

        TorchResponse<EvaluationOrderVO> masterSubmitResp = this.saveOrUpdate(evaluationOrderDto);
        EvaluationOrderVO masterVo = masterSubmitResp.getData().getData();

        List<ProductListDTO> productListDTOs = evaluationOrderDto.getDetailFormItems();


        for(ProductListDTO productListDto : productListDTOs){


            //非必要字段处理
            productListDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
            //主子表关联ID
            productListDto.setEvaluationOrderId(masterVo.getId());
            //产品状态为未下发
            productListDto.setStatus(DicConstant.SalesOrder.PRODUCT_LIST_STATUS_PENDING);

            if (productInfoCreateCachePenetrationBloomFilter.contains(masterVo.getId() + "_" + productListDto.getSerialNumber())){
                //避免误判，再去查看缓存是否存在
                String key = String.format(PRODUCT_INFO_CREATE_KEY, masterVo.getId(), productListDto.getSerialNumber());
                if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(String.format(PRODUCT_INFO_CREATE_KEY, masterVo.getId(), productListDto.getSerialNumber())))){
                    throw new ServiceException("同一个测评订单下不能存在相同序号的产品");
                }
            }


            //提交
            TorchResponse<ProductListVO> detailSubmitResp = productListService.saveOrUpdate(productListDto);

            ProductListVO detailVo = detailSubmitResp.getData().getData();
        }

		TorchResponse response = new TorchResponse();
        response.getData().setData(masterVo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
    }


}
