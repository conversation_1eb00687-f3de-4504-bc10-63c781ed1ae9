package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 能力核验DTO 实体类
* <AUTHOR> @date 2025-08-05
**/
@Data
@ApiModel("能力核验DTO实体类")
public class CapabilityVerificationDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 客户信息id
     */
    @ApiModelProperty("客户信息ID")
    private String customerInfomationId;
    
    /**
	 * 能力核验编号
     **/
    @ApiModelProperty("能力核验编号")
    private String capabilityVerificationNumber;

    /**
     * 委托单位
     */
    @ApiModelProperty("委托单位")
    private String entrustedUnit;

    /**
	 * 确认能力
     **/
    @ApiModelProperty("确认能力")
    private String confirmationOfCapability;

    
    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    private String productModel;
    
    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    private String productName;
    
    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    private String manufacturer;
    
    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
    private String productInformation;

    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    private String status;
    
    /**
	 * 核验结果
     **/
    @ApiModelProperty("核验结果")
    private String verificationResult;
    
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    private String comment;
    
    /**
	 * 能力反馈
     **/
    @ApiModelProperty("能力反馈")
    private String capabilityFeedback;
    
    /**
	 * 反馈人
     **/
    @ApiModelProperty("反馈人")
    private String feedbacker;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;



	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;

}