package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.ProductionTaskAttachmentsVO;
import com.huatek.frame.modules.business.service.ProductionTaskAttachmentsService;
import com.huatek.frame.modules.business.service.dto.ProductionTaskAttachmentsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-11
**/
@Api(tags = "生产任务附件管理")
@RestController
@RequestMapping("/api/productionTaskAttachments")
public class ProductionTaskAttachmentsController {
//
//	@Autowired
//    private ProductionTaskAttachmentsService productionTaskAttachmentsService;
//
//	/**
//	 * 生产任务附件列表
//	 *
//	 * @param dto 生产任务附件DTO 实体对象
//	 * @return
//	 */
//    @Log("生产任务附件列表")
//    @ApiOperation(value = "生产任务附件列表查询")
//    @PostMapping(value = "/productionTaskAttachmentsList", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("productionTaskAttachments:list")
//    public TorchResponse<List<ProductionTaskAttachmentsVO>> query(@RequestBody ProductionTaskAttachmentsDTO dto){
//        return productionTaskAttachmentsService.findProductionTaskAttachmentsPage(dto);
//    }
//
//	/**
//	 * 新增/修改生产任务附件
//	 *
//	 * @param productionTaskAttachmentsDto 生产任务附件DTO实体对象
//	 * @return
//	 * @throws Exception
//	 */
//    @SuppressWarnings("rawtypes")
//    @Log("新增/修改生产任务附件")
//    @ApiOperation(value = "生产任务附件新增/修改操作")
//    @PostMapping(value = "/productionTaskAttachments", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("productionTaskAttachments:add#productionTaskAttachments:edit")
//    public TorchResponse add(@RequestBody ProductionTaskAttachmentsDTO productionTaskAttachmentsDto) throws Exception {
//		// BeanValidatorFactory.validate(productionTaskAttachmentsDto);
//		return productionTaskAttachmentsService.saveOrUpdate(productionTaskAttachmentsDto);
//	}
//
//	/**
//	 * 查询生产任务附件详情
//	 *
//	 * @param id 主键id
//	 * @return
//	 */
//	@SuppressWarnings({ "rawtypes", "unchecked" })
//    @Log("生产任务附件详情")
//    @ApiOperation(value = "生产任务附件详情查询")
//    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("productionTaskAttachments:detail")
//	public TorchResponse detail(@PathVariable(value = "id") String id) {
//		return productionTaskAttachmentsService.findProductionTaskAttachments(id);
//	}
//
//	/**
//	 * 删除生产任务附件
//	 *
//	 * @param ids
//	 * @return
//	 */
//	@SuppressWarnings("rawtypes")
//    @Log("删除生产任务附件")
//    @ApiOperation(value = "生产任务附件删除操作")
//    @TorchPerm("productionTaskAttachments:del")
//    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
//	public TorchResponse delete(@RequestBody String[] ids) {
//		return productionTaskAttachmentsService.delete(ids);
//	}
//
//    @ApiOperation(value = "生产任务附件联动选项值查询")
//    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
//	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
//		return productionTaskAttachmentsService.getOptionsList(id);
//	}
//
//
//
//
//
//    @Log("生产任务附件导出")
//    @ApiOperation(value = "生产任务附件导出")
//    @TorchPerm("productionTaskAttachments:export")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, @RequestBody ProductionTaskAttachmentsDTO dto)
//    {
//        List<ProductionTaskAttachmentsVO> list = productionTaskAttachmentsService.selectProductionTaskAttachmentsList(dto);
//        ExcelUtil<ProductionTaskAttachmentsVO> util = new ExcelUtil<ProductionTaskAttachmentsVO>(ProductionTaskAttachmentsVO.class);
//        util.exportExcel(response, list, "生产任务附件数据");
//    }
//
//    @Log("生产任务附件导入")
//    @ApiOperation(value = "生产任务附件导入")
//    @TorchPerm("productionTaskAttachments:import")
//    @PostMapping("/importData")
//    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
//    {
//        ExcelUtil<ProductionTaskAttachmentsVO> util = new ExcelUtil<ProductionTaskAttachmentsVO>(ProductionTaskAttachmentsVO.class);
//        List<ProductionTaskAttachmentsVO> list = util.importExcel(file.getInputStream());
//        return productionTaskAttachmentsService.importProductionTaskAttachments(list, unionColumns, true, "");
//    }
//
//    @Log("生产任务附件导入模板")
//    @ApiOperation(value = "生产任务附件导入模板下载")
//    @PostMapping("/importTemplate")
//    public void importTemplate(HttpServletResponse response) throws IOException
//    {
//        ExcelUtil<ProductionTaskAttachmentsVO> util = new ExcelUtil<ProductionTaskAttachmentsVO>(ProductionTaskAttachmentsVO.class);
//        util.importTemplateExcel(response, "生产任务附件数据");
//    }
//
//    @Log("根据Ids获取生产任务附件列表")
//    @ApiOperation(value = "生产任务附件 根据Ids批量查询")
//    @PostMapping(value = "/productionTaskAttachmentsList/ids", produces = {"application/json;charset=utf-8"})
//    public TorchResponse getProductionTaskAttachmentsListByIds(@RequestBody List<String> ids) {
//        return productionTaskAttachmentsService.selectProductionTaskAttachmentsListByIds(ids);
//    }


}