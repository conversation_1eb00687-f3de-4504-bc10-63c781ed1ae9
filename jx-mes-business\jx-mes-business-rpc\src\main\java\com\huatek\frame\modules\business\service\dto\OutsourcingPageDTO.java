package com.huatek.frame.modules.business.service.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Date;
import java.sql.Timestamp;

@Data
@ApiModel("外协申请分页查询DTO")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutsourcingPageDTO {

    /**
     * 主键id
     */
    private String id;

    /**
     * 外协编号
     **/
    @ApiModelProperty("外协编号")
    private String outsourcingNumber;

    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")
    private String workOrderNumber;


    /**
     * 产品分类
     **/
    @ApiModelProperty("产品分类")
    private String productCategory;


    /**
     * 订单编号
     **/
    @ApiModelProperty("订单编号")
    private String orderNumber;

    /**
     * 委托单位
     **/
    @ApiModelProperty("委托单位")
    private String entrustedUnit;

    /**
     * 外协部门
     */
    @ApiModelProperty("外协部门")
    private String outsourcingDepartment;


    /**
     * 状态
     **/
    @ApiModelProperty("状态")
    private String status;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String productModel;


    /**
     * 申请时间
     **/
    @ApiModelProperty("申请时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date applicationTime;

    /**
     * 申请人
     **/
    @ApiModelProperty("申请人")
    private String codexTorchApplicant;

    /**
     * 待审批人
     **/
    @ApiModelProperty("待审批人")
    private String codexTorchApprover;

    /**
     * 审批人列表
     **/
    @ApiModelProperty("审批人列表")
    private String codexTorchApprovers;

    /**
     * 试验类型
     */
    @ApiModelProperty("试验类型")
    private String testType;

    /**
     * 流程状态
     **/
    @ApiModelProperty("流程状态")
    private String codexTorchApprovalStatus;

    /**
     * 工作流查询角色
     **/
    @ApiModelProperty("工作流查询角色")
    private String workflowQueryRole;

    /**
     * 页码
     */
    @ApiModelProperty("当前页码")
    private Integer page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty("每页显示大小")
    private Integer limit;
}
