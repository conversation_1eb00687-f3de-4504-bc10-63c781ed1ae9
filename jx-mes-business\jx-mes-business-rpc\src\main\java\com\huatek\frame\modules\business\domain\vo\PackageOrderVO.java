package com.huatek.frame.modules.business.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;

/**
* @description 封装订单VO实体类
* <AUTHOR>
* @date 2025-07-22
**/
@Data
@ApiModel("封装订单DTO实体类")
public class PackageOrderVO implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderNumber;
    
    /**
	 * 订单类型
     **/
    @ApiModelProperty("订单类型")
    @Excel(name = "订单类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderType;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String productModel;
    
    /**
	 * 客户编号
     **/
    @ApiModelProperty("客户编号")
    @Excel(name = "客户编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String customerId0;
    
    /**
	 * 结算单位
     **/
    @ApiModelProperty("结算单位")
    @Excel(name = "结算单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String settlementUnit;
    
    /**
	 * 订单状态
     **/
    @ApiModelProperty("订单状态")
    @Excel(name = "订单状态",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderStatus;

    /**
     * 委托单位
     */
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private  String entrustedUnit;

    /**
	 * 委托人
     **/
    @ApiModelProperty("委托人")
    @Excel(name = "委托人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String principal;
    
    /**
	 * 委托人电话
     **/
    @ApiModelProperty("委托人电话")
    @Excel(name = "委托人电话",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String principalsPhoneNumber;
    
    /**
	 * 接收人
     **/
    @ApiModelProperty("接收人")
    @Excel(name = "接收人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String recipient;
    
    /**
	 * 委托日期
     **/
    @ApiModelProperty("委托日期")
    @Excel(name = "委托日期",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfEntrustment;
    
    /**
	 * 紧急程度
     **/
    @ApiModelProperty("紧急程度")
    @Excel(name = "紧急程度",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String urgencyLevel;
    
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    @Excel(name = "备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String comment;
    
    /**
	 * 子表明细项ID
     **/
    @ApiModelProperty("子表明细项ID")
    private String codexTorchDetailItemIds;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creator;

    /**
	 * 创建人id
     **/
    @ApiModelProperty("创建人id")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}