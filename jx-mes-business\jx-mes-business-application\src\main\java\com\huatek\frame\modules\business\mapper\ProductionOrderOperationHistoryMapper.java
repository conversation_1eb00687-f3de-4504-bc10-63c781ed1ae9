package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import com.huatek.frame.modules.business.domain.ProductionOrderOperationHistory;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderOperationHistoryVO;
import com.huatek.frame.modules.business.service.dto.ProductionOrderOperationHistoryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* 待制工单mapper
* <AUTHOR>
* @date 2025-07-30
**/
public interface ProductionOrderOperationHistoryMapper extends BaseMapper<ProductionOrderOperationHistory> {

	/**
	 * 工单操作历史记录分页
	 *
	 * @param dto
	 * @return
	 */
//	Page<ProductionOrderOperationHistoryVO> selectProductionOrderOperationHistoryPage(ProductionOrderOperationHistoryDTO dto);


	/**
	 * 根据条件查询待制工单列表
	 *
	 * @param dto 待制工单信息
	 * @return 待制工单集合信息
	 */
	List<ProductionOrderOperationHistoryVO> selectProductionOrderOrderOperationHistoryList(ProductionOrderOperationHistoryDTO dto);



    /**
	 * 根据工单查询工单操作历史
	 *
	 * @param id
	 * @return
	 */
	List<ProductionOrderOperationHistoryVO> selectByProductOrder(@Param("id") String id);

}