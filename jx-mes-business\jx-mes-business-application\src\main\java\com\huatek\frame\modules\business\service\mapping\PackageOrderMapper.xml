<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.PackageOrderMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.order_number as orderNumber,
		t.order_type as orderType,
		t.customer_id0 as customerId0,
		t.settlement_unit as settlementUnit,
		t.order_status as orderStatus,
		t.principal as principal,
		t.principals_phone_number as principalsPhoneNumber,
		t.recipient as recipient,
		t.date_of_entrustment as dateOfEntrustment,
		t.urgency_level as urgencyLevel,
		t.`comment` as `comment`,
		t.CODEX_TORCH_DETAIL_ITEM_IDS as codexTorchDetailItemIds,
		t.CODEX_TORCH_CREATOR_ID as codexTorchCreatorId,
		t.CODEX_TORCH_UPDATER as codexTorchUpdater,
		t.CODEX_TORCH_GROUP_ID as codexTorchGroupId,
		t.CODEX_TORCH_CREATE_DATETIME as codexTorchCreateDatetime,
		t.CODEX_TORCH_UPDATE_DATETIME as codexTorchUpdateDatetime,
		t.CODEX_TORCH_DELETED as codexTorchDeleted
	</sql>
	<select id="selectPackageOrderPage" parameterType="com.huatek.frame.modules.business.service.dto.PackageOrderDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.PackageOrderVO">
		select
		<include refid="Base_Column_List" />, u.user_name as creator, p.product_model as productModel, c.entrusted_unit as entrustedUnit
			from package_order t
            LEFT JOIN sys_user u ON u.id = t.CODEX_TORCH_CREATOR_ID
            LEFT JOIN product_information p ON p.CODEX_TORCH_MASTER_FORM_ID = t.id
            LEFT JOIN customer_information_management c ON c.customer_id0 = t.customer_id0
            <where>
                and 1=1
                <if test="orderNumber != null and orderNumber != ''">
                    and t.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="orderType != null and orderType != ''">
                    and t.order_type  = #{orderType}
                </if>
                <if test="productModel != null and productModel != ''">
                    and p.product_model like concat('%', #{productModel}, '%')
                </if>
                <if test="customerId0 != null and customerId0 != ''">
                    and t.customer_id0  = #{customerId0}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and c.entrusted_unit like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="settlementUnit != null and settlementUnit != ''">
                    and t.settlement_unit  like concat('%', #{settlementUnit} ,'%')
                </if>
                <if test="orderStatus != null and orderStatus != ''">
                    and t.order_status  = #{orderStatus}
                </if>
                <if test="principal != null and principal != ''">
                    and t.principal  like concat('%', #{principal} ,'%')
                </if>
                <if test="principalsPhoneNumber != null and principalsPhoneNumber != ''">
                    and t.principals_phone_number  like concat('%', #{principalsPhoneNumber} ,'%')
                </if>
                <if test="recipient != null and recipient != ''">
                    and t.recipient  like concat('%', #{recipient} ,'%')
                </if>
                <if test="dateOfEntrustment != null">
                    and t.date_of_entrustment  = #{dateOfEntrustment}
                </if>
                <if test="urgencyLevel != null and urgencyLevel != ''">
                    and t.urgency_level  = #{urgencyLevel}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  = #{comment}
                </if>
                <if test="creator != null and creator != ''">
                    and u.user_name like concat ('%', #{creator}, '%')
                </if>
                <if test="codexTorchDetailItemIds != null and codexTorchDetailItemIds != ''">
                    and t.CODEX_TORCH_DETAIL_ITEM_IDS  like concat('%', #{codexTorchDetailItemIds} ,'%')
                </if>
                <if test="startDatetime != null and endDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME between #{startCreateDatetime} and #{endCreateDatetime}
                </if>

                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>
     <select id="selectOptionsByCustomerId0" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.entrusted_unit label,
        	t.customer_id0 value
        from customer_information_management t
        WHERE t.customer_id0 != ''
     </select>
     <select id="selectDataLinkageByCustomerId0" parameterType="String"
             resultType="java.util.Map">
        select
            t.settlement_unit as settlementUnit
        from customer_information_management t
        WHERE t.customer_id0 = #{customer_id0}
     </select>

    <select id="selectPackageOrderList" parameterType="com.huatek.frame.modules.business.service.dto.PackageOrderDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.PackageOrderVO">
		select
		<include refid="Base_Column_List" />, u.user_name as creator
			from package_order t
            LEFT JOIN sys_user u ON u.id = t.CODEX_TORCH_CREATOR_ID
            <where>
                and 1=1
                <if test="orderNumber != null and orderNumber != ''">
                    and t.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="orderType != null and orderType != ''">
                    and t.order_type  = #{orderType}
                </if>
                <if test="customerId0 != null and customerId0 != ''">
                    and t.customer_id0  = #{customerId0}
                </if>
                <if test="settlementUnit != null and settlementUnit != ''">
                    and t.settlement_unit  like concat('%', #{settlementUnit} ,'%')
                </if>
                <if test="orderStatus != null and orderStatus != ''">
                    and t.order_status  = #{orderStatus}
                </if>
                <if test="principal != null and principal != ''">
                    and t.principal  like concat('%', #{principal} ,'%')
                </if>
                <if test="principalsPhoneNumber != null and principalsPhoneNumber != ''">
                    and t.principals_phone_number  like concat('%', #{principalsPhoneNumber} ,'%')
                </if>
                <if test="recipient != null and recipient != ''">
                    and t.recipient  like concat('%', #{recipient} ,'%')
                </if>
                <if test="dateOfEntrustment != null">
                    and t.date_of_entrustment  = #{dateOfEntrustment}
                </if>
                <if test="urgencyLevel != null and urgencyLevel != ''">
                    and t.urgency_level  = #{urgencyLevel}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  = #{comment}
                </if>
                <if test="creator != null and creator != ''">
                    and u.user_name like concat ('%', #{creator}, '%')
                </if>
                <if test="codexTorchDetailItemIds != null and codexTorchDetailItemIds != ''">
                    and t.CODEX_TORCH_DETAIL_ITEM_IDS  like concat('%', #{codexTorchDetailItemIds} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectPackageOrderListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.PackageOrderVO">
		select
		<include refid="Base_Column_List" />
			from package_order t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>