package com.huatek.frame.modules.business.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;
import com.huatek.frame.modules.business.domain.Outsourcing;
import com.huatek.frame.modules.business.domain.vo.OutsourcingVO;
import com.huatek.frame.modules.business.service.dto.*;

import java.util.List;


/**
* @description 外协申请Service
* <AUTHOR>
* @date 2025-08-07
**/
public interface OutsourcingService extends IService<Outsourcing> {
    
    /**
	 * 分页查找查找 外协申请
	 * 
	 * @param dto 外协申请dto实体对象
	 * @return 
	 */
	TorchResponse<List<OutsourcingVO>> findOutsourcingApplicationPage(OutsourcingPageDTO requestParam);

    /**
	 * 添加 \修改 外协申请
	 * 
	 * @param requestParam 新增/修改外协申请dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(AddOrUpdateOutsourcingDTO requestParam);
	
	/**
	 * 通过id查找外协申请
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<OutsourcingVO> findOutsourcingDetails(String id);
	
	/**
	 * 删除 外协申请
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 外协申请
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<OutsourcingVO>> getOptionsList(String id);

	/**
	 * 表单审批
	 *
	 * @param formApprovalDTO
	 * @param token
	 * @return
	 */
	TorchResponse approve(FormApprovalDTO formApprovalDTO, String token);

	/**
	 * 审批审批 外协申请
	 *
	 * @param outsourcingDto 用户管理dto实体对象
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse apply(AddOrUpdateOutsourcingDTO outsourcingDto,String token);




    /**
     * 根据条件查询外协申请列表
     *
     * @param dto 外协申请信息
     * @return 外协申请集合信息
     */
    List<OutsourcingVO> selectOutsourcingApplicationList(OutsourcingDTO dto);

    /**
     * 导入外协申请数据
     *
     * @param outsourcingApplicationList 外协申请数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importOutsourcingApplication(List<OutsourcingVO> outsourcingApplicationList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取外协申请数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectOutsourcingApplicationListByIds(List<String> ids);


	/**
	 * 批量修改外协工单状态:审批通过、审批驳回
	 * @param requestParam
	 * @return
	 */
	TorchResponse batchUpdateOutSourcing(OutsourcingUpdateDTO requestParam, String token);

	/**
	 * 分页查询待审批状态下的外协工单
	 * @return
	 */
	TorchResponse findPendingOutsourcings(OutsourcingPageDTO requestParam);

	/**
	 * 分页查询审批历史
	 * @param requestParam 分页请求参数
	 * @return
	 */
	TorchResponse findOutsourcingHistorys(OutsourcingPageDTO requestParam);
}