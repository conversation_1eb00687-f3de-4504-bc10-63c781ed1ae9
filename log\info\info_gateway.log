2025-08-15 09:19:29,463 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/role
2025-08-15 09:19:30,418 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-08-15 09:30:06,795 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-08-15 09:30:06,815 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-08-15 09:30:06,838 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-15 09:30:07,211 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-08-15 09:31:40,938 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-08-15 09:31:44,818 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-08-15 09:31:59,922 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-08-15 10:30:10,754 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-08-15 10:30:10,754 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-08-15 10:30:10,754 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceType
2025-08-15 10:30:10,754 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-08-15 10:30:11,403 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-08-15 10:30:11,520 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/equipmentInventoryList
2025-08-15 10:30:12,127 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EquipmentInventory
2025-08-15 10:30:12,136 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-08-15 10:30:12,290 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-08-15 10:30:13,648 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-08-15 10:30:13,754 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-08-15 10:30:14,029 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-08-15 10:30:14,119 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-08-15 10:30:14,197 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-08-15 10:31:04,818 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/detail/1948298425416200194
2025-08-15 10:31:04,971 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/latestTraceInfo
2025-08-15 10:31:05,318 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-08-15 10:31:05,323 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-08-15 10:31:05,324 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-08-15 10:31:05,359 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-08-15 10:31:05,375 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-08-15 10:31:05,394 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-08-15 10:31:05,401 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-08-15 10:31:05,402 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:31:05,404 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-08-15 10:31:05,473 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-08-15 10:31:05,497 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-08-15 10:31:20,276 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:31:20,328 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:31:21,798 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:31:21,840 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:31:23,176 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:31:23,213 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:31:27,910 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:31:27,944 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:31:54,908 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/detail/1950019329389129729
2025-08-15 10:31:55,002 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/latestTraceInfo
2025-08-15 10:31:57,229 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/detail/1950019329389129729
2025-08-15 10:31:57,284 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/latestTraceInfo
2025-08-15 10:31:57,990 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/detail/1950019329389129729
2025-08-15 10:31:58,048 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/latestTraceInfo
2025-08-15 10:31:58,427 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-08-15 10:31:58,432 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-08-15 10:31:58,454 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-08-15 10:31:58,455 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-08-15 10:31:58,455 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:31:58,455 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-08-15 10:31:58,490 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-08-15 10:31:58,491 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-08-15 10:31:58,581 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-08-15 10:31:58,581 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-08-15 10:31:58,581 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-08-15 10:32:02,246 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:32:02,290 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:32:07,572 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/masterDetailSubmit
2025-08-15 10:32:07,914 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/equipmentInventoryList
2025-08-15 10:32:18,966 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/equipmentInventoryList
2025-08-15 10:32:20,204 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/equipmentInventoryList
2025-08-15 10:32:33,398 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/equipmentInventoryList
2025-08-15 10:32:47,665 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/detail/1950019329389129729
2025-08-15 10:32:47,749 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/latestTraceInfo
2025-08-15 10:32:48,478 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-08-15 10:32:48,481 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-08-15 10:32:48,486 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-08-15 10:32:48,525 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-08-15 10:32:48,525 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-08-15 10:32:48,526 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-08-15 10:32:48,566 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-08-15 10:32:48,580 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-08-15 10:32:48,600 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-08-15 10:32:48,663 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-08-15 10:32:48,665 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-08-15 10:38:22,394 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-08-15 10:38:37,522 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-08-15 10:38:37,556 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-08-15 10:38:37,562 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-08-15 10:38:37,562 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceType
2025-08-15 10:38:37,562 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-08-15 10:38:37,584 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/equipmentInventoryList
2025-08-15 10:38:37,660 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-08-15 10:38:37,660 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EquipmentInventory
2025-08-15 10:38:37,855 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-08-15 10:38:37,988 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-08-15 10:38:38,105 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-08-15 10:38:38,245 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-08-15 10:38:38,306 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-08-15 10:38:38,355 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-08-15 10:38:45,454 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-08-15 10:40:11,857 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/role
2025-08-15 10:40:12,071 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-08-15 10:40:14,648 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/detail/1956184115352494081
2025-08-15 10:40:17,250 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/role
2025-08-15 10:40:17,490 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-08-15 10:48:03,348 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-08-15 10:48:03,799 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-08-15 10:48:40,026 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/detail/null
2025-08-15 10:48:49,416 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menu
2025-08-15 10:48:50,294 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-08-15 10:49:00,309 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/detail/null
2025-08-15 10:49:07,694 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menu
2025-08-15 10:49:07,967 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-08-15 10:49:28,722 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/detail/null
2025-08-15 10:49:37,391 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menu
2025-08-15 10:49:37,582 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-08-15 10:49:54,288 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/detail/null
2025-08-15 10:50:09,141 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menu
2025-08-15 10:50:09,493 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-08-15 11:19:56,997 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-08-15 11:19:56,999 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/SysUser
2025-08-15 11:19:56,999 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
2025-08-15 11:19:57,013 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/users
2025-08-15 11:19:57,186 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/system_user_gender
2025-08-15 11:19:57,320 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/group/cascade
