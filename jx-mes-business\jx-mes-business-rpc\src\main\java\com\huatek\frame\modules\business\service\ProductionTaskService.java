package com.huatek.frame.modules.business.service;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.ProductionTaskVO;
import com.huatek.frame.modules.business.domain.vo.ProductionTaskViewVO;
import com.huatek.frame.modules.business.domain.vo.UnqualifiedProcessVO;
import com.huatek.frame.modules.business.service.dto.*;

import java.util.List;

import java.util.Map;

/**
* @description 生产任务Service
* <AUTHOR>
* @date 2025-08-11
**/
public interface ProductionTaskService {
    
    /**
	 * 分页查找查找 生产任务
	 * 
	 * @param dto 生产任务dto实体对象
	 * @return 
	 */
	TorchResponse<List<ProductionTaskVO>> findProductionTaskPage(ProductionTaskDTO dto);

    /**
	 * 添加 \修改 生产任务
	 * 
	 * @param productionTaskDto 生产任务dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(ProductionTaskDTO productionTaskDto);
	
	/**
	 * 通过id查找生产任务
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<ProductionTaskVO> findProductionTask(String id);
	
	/**
	 * 删除 生产任务
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 生产任务
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<ProductionTaskVO>> getOptionsList(String id);




    /**
     * 联动数据查询
     *
     * @param linkageDataTableName
     * @param conditionalValue
     * @return
     */
    TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue);

    Map<String,String> selectDataLinkageByTechnicalCompetencyNumber(String capability_number);
    /**
     * 根据条件查询生产任务列表
     *
     * @param dto 生产任务信息
     * @return 生产任务集合信息
     */
    List<ProductionTaskVO> selectProductionTaskList(ProductionTaskDTO dto);

    /**
     * 导入生产任务数据
     *
     * @param productionTaskList 生产任务数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importProductionTask(List<ProductionTaskVO> productionTaskList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取生产任务数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectProductionTaskListByIds(List<String> ids);


	TorchResponse abnormalfeedback(AbnormalfeedbackDTO abnormalfeedbackDto);

	TorchResponse outsourcing(AddOrUpdateOutsourcingDTO addOrUpdateOutsourcingDTO);

	TorchResponse saveBatch(List<ProductionTaskDTO> productionTaskDtoList);

	TorchResponse splitOrder(List<SplitOrderProductionTaskDTO> splitOrderProductionTaskDTOList);

	TorchResponse outsourcingPass(String processId);

	TorchResponse outsourcingAccept(String processId);

	/**
	 * 扫码接口
	 *
	 * @param scanCodeDto 扫码请求DTO
	 * @return 扫码响应结果
	 */
	TorchResponse scanCode(ScanCodeDTO scanCodeDto);

	/**
	 * 开始任务接口
	 *
	 * @param startTaskDto 开始任务请求DTO
	 * @return 开始任务响应结果
	 */
	TorchResponse startTask(StartTaskDTO startTaskDto);

	/**
	 * 通过工单编号查询最后一个工序的合格数量
	 *
	 * @param workOrderNumber 工单编号
	 * @return 最后一个工序的合格数量
	 */
	TorchResponse<Long> getLastProcessQualifiedQuantity(String workOrderNumber);

	/**
	 * 通过工单编号查询所有的不合格工序和不合格原因
	 *
	 * @param workOrderNumber 工单编号
	 * @return 工单质量信息
	 */
	TorchResponse<List<UnqualifiedProcessVO>> getUnqualifiedTaskInfo(String workOrderNumber);

	/**
	 * 暂停任务
	 *
	 * @param taskOperationDto 任务操作DTO
	 * @return 操作结果
	 */
	TorchResponse pauseTask(TaskOperationDTO taskOperationDto);

	/**
	 * 恢复任务
	 *
	 * @param id 任务操作DTO
	 * @return 操作结果
	 */
	TorchResponse resumeTask(String id);

	/**
	 * 取消任务
	 *
	 * @param taskOperationDto 任务操作DTO
	 * @return 操作结果
	 */
	TorchResponse cancelTask(TaskOperationDTO taskOperationDto);

	/**
	 * 消除PDA预警
	 *
	 * @param id 生产任务ID
	 * @return 操作结果
	 */
	TorchResponse clearPdaWarning(String id);

	/**
	 * 根据工单号查询工单任务及测试数据
	 * @param workOrderNumber
	 * @return
	 */
	List<ProductionTaskViewVO> selectProductionTaskByProductionOrder(String workOrderNumber);
}