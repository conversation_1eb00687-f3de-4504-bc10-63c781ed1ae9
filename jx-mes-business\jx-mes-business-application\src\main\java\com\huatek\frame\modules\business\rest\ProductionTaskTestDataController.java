package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.ProductionTaskTestDataVO;
import com.huatek.frame.modules.business.service.ProductionTaskTestDataService;
import com.huatek.frame.modules.business.service.dto.ProductionTaskTestDataDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-11
**/
@Api(tags = "生产任务试验数据管理")
@RestController
@RequestMapping("/api/productionTaskTestData")
public class ProductionTaskTestDataController {
//
//	@Autowired
//    private ProductionTaskTestDataService productionTaskTestDataService;
//
//	/**
//	 * 生产任务试验数据列表
//	 *
//	 * @param dto 生产任务试验数据DTO 实体对象
//	 * @return
//	 */
//    @Log("生产任务试验数据列表")
//    @ApiOperation(value = "生产任务试验数据列表查询")
//    @PostMapping(value = "/productionTaskTestDataList", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("productionTaskTestData:list")
//    public TorchResponse<List<ProductionTaskTestDataVO>> query(@RequestBody ProductionTaskTestDataDTO dto){
//        return productionTaskTestDataService.findProductionTaskTestDataPage(dto);
//    }
//
//	/**
//	 * 新增/修改生产任务试验数据
//	 *
//	 * @param productionTaskTestDataDto 生产任务试验数据DTO实体对象
//	 * @return
//	 * @throws Exception
//	 */
//    @SuppressWarnings("rawtypes")
//    @Log("新增/修改生产任务试验数据")
//    @ApiOperation(value = "生产任务试验数据新增/修改操作")
//    @PostMapping(value = "/productionTaskTestData", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("productionTaskTestData:add#productionTaskTestData:edit")
//    public TorchResponse add(@RequestBody ProductionTaskTestDataDTO productionTaskTestDataDto) throws Exception {
//		// BeanValidatorFactory.validate(productionTaskTestDataDto);
//		return productionTaskTestDataService.saveOrUpdate(productionTaskTestDataDto);
//	}
//
//	/**
//	 * 查询生产任务试验数据详情
//	 *
//	 * @param id 主键id
//	 * @return
//	 */
//	@SuppressWarnings({ "rawtypes", "unchecked" })
//    @Log("生产任务试验数据详情")
//    @ApiOperation(value = "生产任务试验数据详情查询")
//    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("productionTaskTestData:detail")
//	public TorchResponse detail(@PathVariable(value = "id") String id) {
//		return productionTaskTestDataService.findProductionTaskTestData(id);
//	}
//
//	/**
//	 * 删除生产任务试验数据
//	 *
//	 * @param ids
//	 * @return
//	 */
//	@SuppressWarnings("rawtypes")
//    @Log("删除生产任务试验数据")
//    @ApiOperation(value = "生产任务试验数据删除操作")
//    @TorchPerm("productionTaskTestData:del")
//    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
//	public TorchResponse delete(@RequestBody String[] ids) {
//		return productionTaskTestDataService.delete(ids);
//	}
//
//    @ApiOperation(value = "生产任务试验数据联动选项值查询")
//    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
//	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
//		return productionTaskTestDataService.getOptionsList(id);
//	}
//
//
//
//
//
//    @Log("生产任务试验数据导出")
//    @ApiOperation(value = "生产任务试验数据导出")
//    @TorchPerm("productionTaskTestData:export")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, @RequestBody ProductionTaskTestDataDTO dto)
//    {
//        List<ProductionTaskTestDataVO> list = productionTaskTestDataService.selectProductionTaskTestDataList(dto);
//        ExcelUtil<ProductionTaskTestDataVO> util = new ExcelUtil<ProductionTaskTestDataVO>(ProductionTaskTestDataVO.class);
//        util.exportExcel(response, list, "生产任务试验数据数据");
//    }
//
//    @Log("生产任务试验数据导入")
//    @ApiOperation(value = "生产任务试验数据导入")
//    @TorchPerm("productionTaskTestData:import")
//    @PostMapping("/importData")
//    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
//    {
//        ExcelUtil<ProductionTaskTestDataVO> util = new ExcelUtil<ProductionTaskTestDataVO>(ProductionTaskTestDataVO.class);
//        List<ProductionTaskTestDataVO> list = util.importExcel(file.getInputStream());
//        return productionTaskTestDataService.importProductionTaskTestData(list, unionColumns, true, "");
//    }
//
//    @Log("生产任务试验数据导入模板")
//    @ApiOperation(value = "生产任务试验数据导入模板下载")
//    @PostMapping("/importTemplate")
//    public void importTemplate(HttpServletResponse response) throws IOException
//    {
//        ExcelUtil<ProductionTaskTestDataVO> util = new ExcelUtil<ProductionTaskTestDataVO>(ProductionTaskTestDataVO.class);
//        util.importTemplateExcel(response, "生产任务试验数据数据");
//    }
//
//    @Log("根据Ids获取生产任务试验数据列表")
//    @ApiOperation(value = "生产任务试验数据 根据Ids批量查询")
//    @PostMapping(value = "/productionTaskTestDataList/ids", produces = {"application/json;charset=utf-8"})
//    public TorchResponse getProductionTaskTestDataListByIds(@RequestBody List<String> ids) {
//        return productionTaskTestDataService.selectProductionTaskTestDataListByIds(ids);
//    }


}