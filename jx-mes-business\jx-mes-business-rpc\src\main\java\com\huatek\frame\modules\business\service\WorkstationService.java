package com.huatek.frame.modules.business.service;

import com.huatek.frame.modules.business.domain.Workstation;
import com.huatek.frame.modules.business.service.dto.WorkstationDTO;
import com.huatek.frame.modules.business.domain.vo.WorkstationVO;
import com.huatek.frame.common.response.TorchResponse;
import java.util.List;


/**
* @description 工作站Service
* <AUTHOR>
* @date 2025-07-17
**/
public interface WorkstationService {
    
    /**
	 * 分页查找查找 工作站
	 * 
	 * @param dto 工作站dto实体对象
	 * @return 
	 */
	TorchResponse<List<WorkstationVO>> findWorkstationPage(WorkstationDTO dto);

    /**
	 * 添加 \修改 工作站
	 * 
	 * @param workstationDto 工作站dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(WorkstationDTO workstationDto);
	
	/**
	 * 通过id查找工作站
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<WorkstationVO> findWorkstation(String id);
	
	/**
	 * 删除 工作站
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 工作站
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<WorkstationVO>> getOptionsList(String id);




    /**
     * 根据条件查询工作站列表
     *
     * @param dto 工作站信息
     * @return 工作站集合信息
     */
    List<WorkstationVO> selectWorkstationList(WorkstationDTO dto);

    /**
     * 导入工作站数据
     *
     * @param workstationList 工作站数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importWorkstation(List<WorkstationVO> workstationList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取工作站数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectWorkstationListByIds(List<String> ids);

	/**
	 * 根据扫码枪编号查询工作站
	 *
	 * @param scannerGunNumber 扫码枪编号
	 * @return 工作站信息
	 */
	TorchResponse<WorkstationVO> findWorkstationByScannerGunNumber(String scannerGunNumber);

}