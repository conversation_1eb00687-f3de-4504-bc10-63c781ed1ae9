package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderVO;
import com.huatek.frame.modules.business.service.AwaitingProductionOrderService;
import com.huatek.frame.modules.business.service.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
* <AUTHOR>
* @date 2025-07-30
**/
@Api(tags = "生产工单管理")
@RestController
@RequestMapping("/api/productionOrder")
public class ProductionOrderController {

	@Autowired
    private AwaitingProductionOrderService awaitingProductionOrderService;

	/**
	 * 工单列表
	 * 
	 * @param dto 待制工单DTO 实体对象
	 * @return
	 */
    @Log("工单列表")
    @ApiOperation(value = "工单列表查询")
    @PostMapping(value = "/awaitingProductionOrderList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionOrder:list")
    public TorchResponse<List<ProductionOrderVO>> query(@RequestBody ProductionOrderDTO dto){
        return awaitingProductionOrderService.findAwaitingProductionOrderPage(dto);
    }

	/**
	 * 新增/修改工单
	 * 
	 * @param awaitingProductionOrderDto 待制工单DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改待制工单")
    @ApiOperation(value = "待制工单新增/修改操作")
    @PostMapping(value = "/awaitingProductionOrder", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionOrder:add#productionOrder:edit")
    public TorchResponse add(@RequestBody ProductionOrderDTO awaitingProductionOrderDto) throws Exception {
		// BeanValidatorFactory.validate(awaitingProductionOrderDto);
		return awaitingProductionOrderService.saveOrUpdate(awaitingProductionOrderDto);
	}

	/**
	 * 查询待制工单详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("工单详情")
    @ApiOperation(value = "待制工单详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionOrder:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return awaitingProductionOrderService.findAwaitingProductionOrder(id);
	}
    /**
     * 工单查看
     *
     * @param id 主键id
     * @return
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("工单查看")
    @ApiOperation(value = "工单查看")
    @GetMapping(value = "/view/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("awaitingProductionOrder:detail")
    public TorchResponse view(@PathVariable(value = "id") String id) {
        return awaitingProductionOrderService.findAwaitingProductionOrderView(id);
    }

    @ApiOperation(value = "待制工单联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return awaitingProductionOrderService.getOptionsList(id);
	}



    @ApiOperation(value = "待制工单 自动填充数据查询")
    @GetMapping(value = "/linkageData/{linkageDataTableName}/{conditionalValue}", produces = { "application/json;charset=utf-8" })
    public TorchResponse getLinkageData(@PathVariable(value = "linkageDataTableName") String linkageDataTableName,
    									@PathVariable(value = "conditionalValue") String conditionalValue) {
		return awaitingProductionOrderService.getLinkageData(linkageDataTableName, conditionalValue);
	}

    @Log("根据Ids获取待制工单列表")
    @ApiOperation(value = "待制工单 根据Ids批量查询")
    @PostMapping(value = "/awaitingProductionOrderList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getAwaitingProductionOrderListByIds(@RequestBody List<String> ids) {
        return awaitingProductionOrderService.selectAwaitingProductionOrderListByIds(ids);
    }

    @Log("指派负责人")
    @ApiOperation(value = "指派负责人")
    @TorchPerm("productionOrder:setResponsiblePerson")
    @PostMapping(value = "/setResponsiblePerson", produces = {"application/json;charset=utf-8"})
    public TorchResponse setResponsiblePerson(@RequestBody ProductionOrderDTO awaitingProductionOrderDto) {
        return awaitingProductionOrderService.setResponsiblePerson(awaitingProductionOrderDto);
    }


    @Log("复制工单")
    @ApiOperation(value = "复制工单")
    @TorchPerm("productionOrder:copy")
    @PostMapping(value = "/copyProductionOrder", produces = {"application/json;charset=utf-8"})
    public TorchResponse copyProductionOrder(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.copyProductionOrder(productionOrderDTO);
    }
    @Log("分单")
    @ApiOperation(value = "分单")
    @TorchPerm("productionOrder:split")
    @PostMapping(value = "/splitProductionOrder", produces = {"application/json;charset=utf-8"})
    public TorchResponse splitProductionOrder(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.splitProductionOrder(productionOrderDTO);
    }

    @Log("消除pda预警")
    @ApiOperation(value = "消除pda预警")
    @TorchPerm("productionOrder:cancelPdaWarning")
    @PostMapping(value = "/cancelPdaWarning", produces = {"application/json;charset=utf-8"})
    public TorchResponse cancelPdaWarning(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.cancelPdaWarning(productionOrderDTO);
    }
    @Log("暂停")
    @ApiOperation(value = "暂停")
    @TorchPerm("productionOrder:pause")
    @PostMapping(value = "/pauseProductionOrder", produces = {"application/json;charset=utf-8"})
    public TorchResponse pauseProductionOrder(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.pauseProductionOrder(productionOrderDTO);
    }
    @Log("恢复")
    @ApiOperation(value = "恢复")
    @TorchPerm("productionOrder:restore")
    @PostMapping(value = "/restoreProductionOrder", produces = {"application/json;charset=utf-8"})
    public TorchResponse restoreProductionOrder(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.restoreProductionOrder(productionOrderDTO);
    }

    @Log("取消")
    @ApiOperation(value = "取消")
    @TorchPerm("productionOrder:cancel")
    @PostMapping(value = "/cancelProductionOrder", produces = {"application/json;charset=utf-8"})
    public TorchResponse cancelProductionOrder(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.cancelProductionOrder(productionOrderDTO);
    }
    @Log("入库")
    @ApiOperation(value = "入库")
    @TorchPerm("awaitingProductionOrder:instore")
    @PostMapping(value = "/productionOrderInstore", produces = {"application/json;charset=utf-8"})
    public TorchResponse productionOrderInstore(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.productionOrderInstore(productionOrderDTO);
    }

    @Log("不可筛")
    @ApiOperation(value = "不可筛")
    @TorchPerm("productionOrder:noScreened")
    @PostMapping(value = "/connotScreened", produces = {"application/json;charset=utf-8"})
    public TorchResponse connotScreened(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.connotScreened(productionOrderDTO);
    }

    @Log("外协申请")
    @ApiOperation(value = "外协申请")
    @TorchPerm("productionOrder:outSourcingApply")
    @PostMapping(value = "/outSourcingApply", produces = {"application/json;charset=utf-8"})
    public TorchResponse outSourcingApply(@RequestBody AddOrUpdateOutsourcingDTO addOrUpdateOutsourcingDTO) {
        return awaitingProductionOrderService.outSourcingApply(addOrUpdateOutsourcingDTO);
    }

    @Log("任务下发")
    @ApiOperation(value = "任务下发")
    @TorchPerm("productionOrder:issueTask")
    @PostMapping(value = "/issueTask", produces = {"application/json;charset=utf-8"})
    public TorchResponse issueProductionOrderTask(@RequestBody ProductionOrderDTO productionOrderDTO) {
        return awaitingProductionOrderService.issueProductionOrderTask(productionOrderDTO);
    }
}