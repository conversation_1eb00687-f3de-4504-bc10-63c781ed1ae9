package com.huatek.frame.modules.business.service.impl;


import java.sql.Timestamp;
import java.sql.Wrapper;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.modules.bpm.constant.ApprovalStatus;
import com.huatek.frame.modules.bpm.constant.ProcessConstant;
import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;
import com.huatek.frame.modules.bpm.dto.ProcessFormDTO;
import com.huatek.frame.modules.bpm.service.ProcessInstanceProxyService;
import com.huatek.frame.modules.bpm.service.ProcessPrivilegeService;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.domain.vo.*;
import com.huatek.frame.modules.business.mapper.*;
import com.huatek.frame.modules.business.service.*;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.modules.system.domain.SysGroup;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysGroupVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import com.huatek.frame.modules.system.service.SysGroupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import org.springframework.util.ObjectUtils;


/**
 * 待制工单 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "awaitingProductionOrder")
//@RefreshScope
@Slf4j
public class AwaitingProductionOrderServiceImpl implements AwaitingProductionOrderService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private AwaitingProductionOrderMapper awaitingProductionOrderMapper;

	private String processBusinessKey = "待制工单";

	@DubboReference
	private ProcessInstanceProxyService processInstanceProxyService;
	@DubboReference
	private ProcessPrivilegeService processPrivilegeService;
	@Autowired
	private CustomerInformationManagementMapper customerInformationManagementMapper;
	@Autowired
	private ProductionOrderOperationHistoryMapper productionOrderOperationHistoryMapper;
	@Autowired
    private ProductListMapper productListMapper;
	@Autowired
	private ProductManagementMapper productManagementMapper;
	@DubboReference
    private SysGroupService sysGroupService;
	@Autowired
	private CapabilityReviewService capabilityReviewService;

	@Autowired
	private EvaluationOrderMapper evaluationOrderMapper;
	@Autowired
	private CustomerProcessSchemeMapper customerProcessSchemeMapper;
	@Autowired
	private CustomerExperimentProjectMapper customerExperimentProjectMapper;
	@Autowired
	private CustomerExperimentProjectDataMapper customerExperimentProjectDataMapper;
	@Autowired
	private StandardProcessManagementMapper standardProcessManagementMapper;
	@Autowired
	private CodeManagementService codeManagementService;
	@Autowired
	private ExperimentProjectMapper experimentProjectMapper;
	@Autowired
	private WorkstationMapper workstationMapper;
	@Autowired
	private ProductInformationManagementMapper productInformationManagementMapper;

	@Autowired
	private OutsourcingService outsourcingService;
	@Autowired
	private ProductionTaskService productionTaskService;
	@Autowired
	private ProductionOrderResultService productionOrderResultService;


	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public AwaitingProductionOrderServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<ProductionOrderVO>> findAwaitingProductionOrderPage(ProductionOrderDTO dto) {
		String currentUser = SecurityContextHolder.getCurrentUserName();
		//Codex - 流程人员流程查看数据权限控制
		if(!processPrivilegeService.hasViewProcessPrivilege(dto.getId(),currentUser,processBusinessKey)){
			log.info("用户无权限查看流程, currentUser:{},processBusinessKey:{}",currentUser,processBusinessKey);
			TorchResponse<List<ProductionOrderVO>> response = new TorchResponse<List<ProductionOrderVO>>();
			response.getData().setData(new ArrayList<>());
			response.setStatus(200);
			response.getData().setCount(0L);
			return response;
		}

		//判断当前登录人是否是可靠性部门，如果是可靠性部门且角色不是调度则只查看负责人是当前登录人的数据,其他情况查询全部数据
		TorchResponse<SysGroupVO> group = sysGroupService.findGroup(SecurityContextHolder.getCurrentUserGroupId());
		//查询当前用户角色
		List<String> roles = awaitingProductionOrderMapper.selectCurrentUserRoles(SecurityContextHolder.getCurrentUserId());
		if(group.getData().getData().getGroupCode().equals(DicConstant.Group.GROUP_KEKAOXING) && !roles.contains(DicConstant.Role.ROLE_DIAODU)){
			dto.setResponsiblePerson(SecurityContextHolder.getCurrentUserGroupId());
		}
		//Codex - 流程人员流程查看数据权限控制
		 if (dto.getWorkflowQueryRole().equals("applicant")){
			if(currentUser.equals(ProcessConstant.SUPER_USER)){
				dto.setCodexTorchApplicant("");
			}else{
				dto.setCodexTorchApplicant(currentUser);
			}
		}else if(dto.getWorkflowQueryRole().equals("approver")){
			//多审批人处理
			dto.setCodexTorchApprover(null);
			dto.setCodexTorchApprovers(currentUser);
			if(StringUtils.isEmpty(dto.getCodexTorchApprovalStatus()) || currentUser.equals(ProcessConstant.SUPER_USER)) {
				dto.setCodexTorchApprovalStatus("待审批|已审批|已驳回");
			}else {
				dto.setCodexTorchApprovalStatus(dto.getCodexTorchApprovalStatus());
			}
		}
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<ProductionOrderVO> awaitingProductionOrders = awaitingProductionOrderMapper.selectAwaitingProductionOrderPage(dto);
		TorchResponse<List<ProductionOrderVO>> response = new TorchResponse<List<ProductionOrderVO>>();
		response.getData().setData(awaitingProductionOrders);
		response.setStatus(200);
		response.getData().setCount(awaitingProductionOrders.getTotal());
		return response;
	}

	/**
	 * 多级审批，不同人看到的审批状态不同
	 *
	 * @param updateStatusVOVOList
	 * @param currentUser
	 */
	private void updateApproveStatusForCurrentUser(List<ProductionOrderVO> updateStatusVOVOList,String currentUser){
		for(ProductionOrderVO updateStatusVO : updateStatusVOVOList){
			String currentApprover = updateStatusVO.getCodexTorchApprover();
			String approvers = updateStatusVO.getCodexTorchApprovers();

			if(StringUtils.isEmpty(approvers)){
				continue;
			}

			//当前用户不在审批人之列，不能处理记录
			if(!approvers.contains(currentUser)){
				continue;
			}

			//当前用户是当前审批人,审批状态是待审批，不需要更新状态
			if(currentApprover.contains(currentUser)
					&& updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())){
				continue;
			}

			//如果当前用户处在当前审批者之前，更新审批状态为已审批
			if(!currentApprover.equals(currentUser) && (approvers.indexOf(currentApprover) > approvers.indexOf(currentUser))){
				if(updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())) {
					updateStatusVO.setCodexTorchApprovalStatus(ApprovalStatus.APPROVED.getName());
				}
			}

			//如果当前用户在已审批用户中但不是最后一个审批用户，更新审批状态为已审批
			if(!currentApprover.equals(currentUser) && !approvers.endsWith(currentUser)) {
				if (approvers.contains(currentUser)) {
					if (updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())) {
						updateStatusVO.setCodexTorchApprovalStatus(ApprovalStatus.APPROVED.getName());
					}
				}
			}
		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(ProductionOrderDTO awaitingProductionOrderDto) {

//        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(awaitingProductionOrderDto.getCodexTorchDeleted())) {
            awaitingProductionOrderDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = awaitingProductionOrderDto.getId();
		ProductionOrder entity = new ProductionOrder();
        BeanUtils.copyProperties(awaitingProductionOrderDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			throw new ServiceException("工单主键不能为空");
		} else {
			awaitingProductionOrderMapper.updateById(entity);
		}
		TorchResponse response = new TorchResponse();
        ProductionOrderVO vo = new ProductionOrderVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ProductionOrderVO> findAwaitingProductionOrder(String id) {
		ProductionOrderVO vo = new ProductionOrderVO();
		if (!HuatekTools.isEmpty(id)) {
			vo= awaitingProductionOrderMapper.selectProductionOrderById(id);
			if(HuatekTools.isEmpty(vo)) {
				throw new ServiceException("查询失败");
			}
			//BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<ProductionOrderVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ProductionOrderViewVO> findAwaitingProductionOrderView(String id) {
		ProductionOrderViewVO vo = new ProductionOrderViewVO();
		if (!HuatekTools.isEmpty(id)) {
			ProductionOrderVO ordervo= awaitingProductionOrderMapper.selectProductionOrderById(id);
			BeanUtils.copyProperties(ordervo,vo);
			ProductionOrder relateOrder= awaitingProductionOrderMapper.selectById(ordervo.getRelatedWorkOrder());
			if(!ObjectUtils.isEmpty(relateOrder))
				vo.setRelatedWorkOrder(relateOrder.getWorkOrderNumber());

//			vo.setReport();//TODO 查询报告
			//查询操作历史
			List<ProductionOrderOperationHistoryVO> histories = productionOrderOperationHistoryMapper.selectByProductOrder(ordervo.getId());
			vo.setOperatHistory(histories);
			//查询工单任务数据
			List<ProductionTaskViewVO> taskVOS = productionTaskService.selectProductionTaskByProductionOrder(ordervo.getWorkOrderNumber());
			vo.setExperimentProjects(taskVOS);
		}
		TorchResponse<ProductionOrderViewVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<ProductionOrder> awaitingProductionOrderList = awaitingProductionOrderMapper.selectBatchIds(Arrays.asList(ids));
        for (ProductionOrder awaitingProductionOrder : awaitingProductionOrderList) {
            awaitingProductionOrder.setCodexTorchDeleted(Constant.DEFAULT_YES);
            awaitingProductionOrderMapper.updateById(awaitingProductionOrder);
        }
		//awaitingProductionOrderMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("orderNumber",awaitingProductionOrderMapper::selectOptionsByOrderNumber);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("entrustedUnit",awaitingProductionOrderMapper::selectOptionsByEntrustedUnit);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("productModel",awaitingProductionOrderMapper::selectOptionsByProductModel);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("predecessorWorkOrder",awaitingProductionOrderMapper::selectOptionsByPredecessorWorkOrder);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("relatedWorkOrder",awaitingProductionOrderMapper::selectOptionsByRelatedWorkOrder);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("responsiblePerson",awaitingProductionOrderMapper::selectOptionsByResponsiblePerson);
			selectOptionsFuncMap.put("processCode3",awaitingProductionOrderMapper::selectOptionsByProcessCode3);
			selectOptionsFuncMap.put("assoWoPredProc",awaitingProductionOrderMapper::selectOptionsByAssoWoPredProc);
			selectOptionsFuncMap.put("workstation",awaitingProductionOrderMapper::selectOptionsByWorkstation);
			selectOptionsFuncMap.put("deviceType",awaitingProductionOrderMapper::selectOptionsByDeviceType);
			selectOptionsFuncMap.put("productInformation1",awaitingProductionOrderMapper::selectOptionsByProductInformation1);
			selectOptionsFuncMap.put("testingTeam",awaitingProductionOrderMapper::selectOptionsByTestingTeam);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }
  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}


	@Override
	@Transactional
	public TorchResponse approve(FormApprovalDTO formApprovalDTO, String token) {
		log.info("表单审批,formApprovalDTO:{}",formApprovalDTO);

		ProcessFormDTO processFormDTO = new ProcessFormDTO();
		BeanUtils.copyProperties(formApprovalDTO,processFormDTO);
		processFormDTO.setBusinessKey(processBusinessKey);
		SysProcessRecordVO processRecordResp = processInstanceProxyService.approve(processFormDTO,token);

		ProductionOrder updateProcessStatusEntity = new ProductionOrder();
		updateProcessStatusEntity.setId(processRecordResp.getFormId());
		updateProcessStatusEntity.setCodexTorchApprover(processRecordResp.getApprover());
		updateProcessStatusEntity.setCodexTorchApprovers(processRecordResp.getApprovers());

		updateProcessStatusEntity.setCodexTorchApprovalStatus(processRecordResp.getApprovalStatus());
		if(processRecordResp.getApprovalStatus().equals(ApprovalStatus.PENDING_REAPPLY.getName()))
			updateProcessStatusEntity.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_REJECT);
		if(processRecordResp.getApprovalStatus().equals(ApprovalStatus.APPROVED.getName()))
			updateProcessStatusEntity.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED);
		awaitingProductionOrderMapper.updateById(updateProcessStatusEntity);

		log.info("表单审批完成,processRecordResp:{}",processRecordResp);
		//审批通过后将该工单绑定的工序方案设置为可查询
		CustomerProcessScheme customerProcessScheme = customerProcessSchemeMapper.selectByProductionOrder(updateProcessStatusEntity.getId());
		customerProcessScheme.setStatus(DicConstant.CommonDic.DIC_YES);
		customerProcessSchemeMapper.updateById(customerProcessScheme);
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse apply(ProductionOrderDTO awaitingProductionOrderDto, String token) {
		log.info("表单提交申请,ProductionOrderDto:{}",awaitingProductionOrderDto);
		ProductionOrder order = awaitingProductionOrderMapper.selectById(awaitingProductionOrderDto.getId());
		JSONObject json = securityUser.currentUser(token);
		String currentUser = json.getString("userName");
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_WAITAPPROVE);
		order.setCodexTorchApplicant(currentUser);
		order.setCodexTorchApprovalStatus(ApprovalStatus.PENDING_APPROVAL.getName());
		awaitingProductionOrderMapper.updateById(order);
		//CodeX 表单工作流绑定
		ProcessFormDTO processFormDTO = new ProcessFormDTO();
		processFormDTO.setProcessDefinitionKey("SimpleApprovalProcess");
		processFormDTO.setBusinessKey(processBusinessKey);
		processFormDTO.setFormId(awaitingProductionOrderDto.getId());
		SysProcessRecordVO processRecordResp = processInstanceProxyService.startProcessByKey(processFormDTO,token);
		ProductionOrder updateProcessStatusEntity = new ProductionOrder();
		updateProcessStatusEntity.setId(awaitingProductionOrderDto.getId());
		updateProcessStatusEntity.setCodexTorchApplicant(processRecordResp.getApplicant());
		updateProcessStatusEntity.setCodexTorchApprover(processRecordResp.getApprover());

		//更新审批人列表
		updateProcessStatusEntity.setCodexTorchApprovers(processRecordResp.getApprovers());

		updateProcessStatusEntity.setCodexTorchApprovalStatus(processRecordResp.getApprovalStatus());
		awaitingProductionOrderMapper.updateById(updateProcessStatusEntity);
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
//		return updateVoResp;
	}

	@Override
	public TorchResponse getExperimentProjectByPlan(ExperimentProjectDTO dxperimentProjectDTO) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",dxperimentProjectDTO.getCodexTorchMasterFormId());
		List<ExperimentProject> list = experimentProjectMapper.selectList(wrapper);
		List<ExperimentProjectVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			ExperimentProjectVO vo = new ExperimentProjectVO();
			BeanUtils.copyProperties(x,vo);
			StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(x.getProcessCode3());
			StandardProcessManagement assoWoPredstandardProcess =standardProcessManagementMapper.selectById(x.getAssoWoPredProc());
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			ProductInformationManagement productInformationManagement=productInformationManagementMapper.selectById(x.getProductInformation1());
			TorchResponse<SysGroupVO> response = sysGroupService.findGroup(x.getTestingTeam());
			if(!ObjectUtils.isEmpty(standardProcessManagement))
				vo.setProcessCode3Name(standardProcessManagement.getProcessName2());
			if(!ObjectUtils.isEmpty(assoWoPredstandardProcess))
				vo.setAssoWoPredProcName(assoWoPredstandardProcess.getProcessName2());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(productInformationManagement))
				vo.setProductInformation1Name(productInformationManagement.getFileName());
			if(!ObjectUtils.isEmpty(response.getData().getData()))
				vo.setTestingTeamName(response.getData().getData().getGroupName());
			voList.add(vo);
		});
		TorchResponse response = new TorchResponse();
		response.getData().setData(voList);
		return response;
	}
	@Override
	public TorchResponse getExperimentProjectByCustomerPlan(CustomerExperimentProjectDTO customerExperimentProjectDTO) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",customerExperimentProjectDTO.getCodexTorchMasterFormId());
		List<CustomerExperimentProject> list = customerExperimentProjectMapper.selectList(wrapper);
		List<CustomerExperimentProjectVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			CustomerExperimentProjectVO vo = new CustomerExperimentProjectVO();
			BeanUtils.copyProperties(x,vo);
			StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(x.getProcessCode3());
			StandardProcessManagement assoWoPredstandardProcess =standardProcessManagementMapper.selectById(x.getAssoWoPredProc());
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			ProductInformationManagement productInformationManagement=productInformationManagementMapper.selectById(x.getProductInformation1());
			TorchResponse<SysGroupVO> response = sysGroupService.findGroup(x.getTestingTeam());
			if(!ObjectUtils.isEmpty(standardProcessManagement))
				vo.setProcessCode3Name(standardProcessManagement.getProcessName2());
			if(!ObjectUtils.isEmpty(assoWoPredstandardProcess))
				vo.setAssoWoPredProcName(assoWoPredstandardProcess.getProcessName2());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(productInformationManagement))
				vo.setProductInformation1Name(productInformationManagement.getFileName());
			if(!ObjectUtils.isEmpty(response.getData().getData()))
				vo.setTestingTeamName(response.getData().getData().getGroupName());
			voList.add(vo);
		});
		TorchResponse response = new TorchResponse();
		response.getData().setData(voList);
		return response;
	}

	@Override
	public TorchResponse getExperimentProjectByProcess(String[] ids) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.in("id",ids);
		List<StandardProcessManagement> list = standardProcessManagementMapper.selectList(wrapper);
		List<StandardProcessManagementVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			StandardProcessManagementVO vo = new StandardProcessManagementVO();
			BeanUtils.copyProperties(x,vo);
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			TorchResponse<SysGroupVO> response = sysGroupService.findGroup(x.getTestingTeam());
			vo.setProcessCode3Name(x.getProcessName2());
			vo.setProcessCode3(x.getId());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(response.getData().getData()))
				vo.setTestingTeamName(response.getData().getData().getGroupName());
			voList.add(vo);
		});
		TorchResponse response = new TorchResponse();
		response.getData().setData(voList);
		return response;
	}

	@Override
	public TorchResponse restoreProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(!order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PAUSE))
			throw new ServiceException("工单不是暂停状态不能进行恢复操作");
		order.setWorkOrderNumber(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PROGRESS);
		awaitingProductionOrderMapper.updateById(order);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("暂停恢复");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse issueProductionOrderTask(ProductionOrderDTO productionOrderDTO) {
		List<ProductionTaskDTO> productionTaskList = new ArrayList<>();
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		ProductList productList = productListMapper.selectById(order.getProduct());
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("work_order",productionOrderDTO.getId());
		CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(wrapper);
		CustomerExperimentProjectDTO customerExperimentProjectDTO = new CustomerExperimentProjectDTO();
		customerExperimentProjectDTO.setCodexTorchMasterFormId(scheme.getId());
		wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",scheme.getId());
		List<CustomerExperimentProjectVO> customerEperimentProjects= customerExperimentProjectMapper.selectCustomerExperimentProjectList(customerExperimentProjectDTO);
		customerEperimentProjects.stream().forEach(x->{
			ProductionTaskDTO task = new ProductionTaskDTO();
			task.setWorkOrderNumber(order.getWorkOrderNumber());
			task.setInspectionQuantity2(order.getQuantity());
			task.setProcessName2(x.getProcessCode3Name());
			task.setProcessCode(x.getProcessCode3());
			task.setTestBasis(x.getTestBasis());
			task.setDisplayNumber(x.getDisplayNumber());
			task.setCustomerProcessName(x.getCustomerProcessName());
			task.setTestConditions(x.getTestConditions());
			task.setJudgmentCriteria(x.getJudgmentCriteria());
			task.setTicketLevel(productList.getTaskLevel());
			task.setScheduledStartTime(x.getEstimatedStartTime());
			task.setScheduledEndTime(x.getEstimatedEndTime());
			task.setProductName(productList.getProductName());
			task.setWorkstation(x.getWorkstation());
			task.setProductModel(productList.getProductModel());
			task.setManufacturer(productList.getManufacturer());
			task.setBatchNumber(productList.getProductionBatch());
			task.setEntrustedUnit(scheme.getEntrustedUnit());
			task.setGrouping(x.getGrouping());
			task.setTestMethodology(x.getTestMethodology());
			task.setTestType(productList.getTestType());
			task.setExecutionSequence(x.getExecutionSequence());
			StandardProcessManagement standardProcessManagement = standardProcessManagementMapper.selectById(x.getAssoWoPredProc());
			if(!ObjectUtils.isEmpty(standardProcessManagement))
				task.setAssoWoPredProc(standardProcessManagement.getProcessName2());
			ProductionOrder relateOrder = awaitingProductionOrderMapper.selectById(order.getRelatedWorkOrder());
			if(!ObjectUtils.isEmpty(relateOrder))
				task.setRelatedWorkOrder(order.getRelatedWorkOrder());
			task.setBelongingTeam2(x.getTestingTeam());
			TorchResponse<SysGroupVO> testTeam = sysGroupService.findUserByGroupId(x.getTestingTeam());
			TorchResponse<SysGroupVO> group =sysGroupService.findUserByGroupId(testTeam.getData().getData().getGroupParentId());
			task.setDepartment(group.getData().getData().getId());
			productionTaskList.add(task);
		});
		productionTaskService.saveBatch(productionTaskList);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse getBindProcessSchemePlan(ProductionOrderDTO productionOrderDTO) {
		CustomerProcessScheme  customerProcessScheme = customerProcessSchemeMapper.selectByProductionOrder(productionOrderDTO.getId());
		if(ObjectUtils.isEmpty(customerProcessScheme)){
			TorchResponse response = new TorchResponse();
			response.setStatus(Constant.REQUEST_SUCCESS);
			return response;
		}
		CustomerProcessSchemeVO schemeVO = new CustomerProcessSchemeVO();
		schemeVO.setComment(customerProcessScheme.getComment());
		schemeVO.setReportComment(customerProcessScheme.getReportComment());
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",customerProcessScheme.getId());
		List<CustomerExperimentProject> list = customerExperimentProjectMapper.selectList(wrapper);
		List<CustomerExperimentProjectVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			CustomerExperimentProjectVO vo = new CustomerExperimentProjectVO();
			BeanUtils.copyProperties(x,vo);
			StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(x.getProcessCode3());
			StandardProcessManagement assoWoPredstandardProcess =standardProcessManagementMapper.selectById(x.getAssoWoPredProc());
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			ProductInformationManagement productInformationManagement=productInformationManagementMapper.selectById(x.getProductInformation1());
			TorchResponse<SysGroupVO> response = sysGroupService.findGroup(x.getTestingTeam());
			if(!ObjectUtils.isEmpty(standardProcessManagement))
				vo.setProcessCode3Name(standardProcessManagement.getProcessName2());
			if(!ObjectUtils.isEmpty(assoWoPredstandardProcess))
				vo.setAssoWoPredProcName(assoWoPredstandardProcess.getProcessName2());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(productInformationManagement))
				vo.setProductInformation1Name(productInformationManagement.getFileName());
			if(!ObjectUtils.isEmpty(response.getData().getData()))
				vo.setTestingTeamName(response.getData().getData().getGroupName());
			voList.add(vo);
		});
		schemeVO.setCustomerExperimentProjectItems(voList);
		TorchResponse response = new TorchResponse();
		response.getData().setData(schemeVO);
		return response;
	}

	@Override
	public TorchResponse getStandProcessForCustomerPlan(StandardProcessManagementDTO standardProcessManagementDTO) {
		StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(standardProcessManagementDTO.getId());
		TorchResponse response = new TorchResponse();
		response.getData().setData(standardProcessManagement);
		return response;
	}

	@Override
	public TorchResponse copyProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(productionOrderDTO.getCopyType().equals(DicConstant.ProductionOrder.COPY_TYPE_SAME)){
			if(order.getWorkOrderNumber().contains("-")){
				String[] nums = order.getWorkOrderNumber().split("-");
				order.setWorkOrderNumber(nums[0]+String.valueOf(Integer.valueOf(nums[1])+1));
			}else{
				order.setWorkOrderNumber(order.getWorkOrderNumber()+"-1");
			}
		}
		if(productionOrderDTO.getCopyType().equals(DicConstant.ProductionOrder.COPY_TYPE_CHILD)){
			order.setWorkOrderNumber(order.getWorkOrderNumber()+"-1");
		}
		order.setId(null);
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT);
		order.setCodexTorchApplicant(null);
		order.setCodexTorchApprovers(null);
		order.setCodexTorchApprovalStatus(null);
		order.setCodexTorchApprover(null);
		order.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.insert(order);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("复制");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	@Transactional
	public TorchResponse splitProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		List<SplitOrderProductionTaskDTO> splitList = new ArrayList<>();
		//将工单的数量设置为原始工单数量
		order.setQuantity(productionOrderDTO.getOldQquantity());
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.ne("id",order.getId());
		wrapper.like("work_order_number",order.getWorkOrderNumber());
		wrapper.orderByDesc("work_order_number");
		List<ProductionOrder> orderlist =awaitingProductionOrderMapper.selectList(wrapper);
		ProductionOrder lastOrder = orderlist.get(orderlist.size()-1);
		String lastOrderNumber = lastOrder.getWorkOrderNumber();
		String orderNumber="";
		if(lastOrderNumber.contains("-")){
			orderNumber=lastOrderNumber.split("-")[0]+(Integer.valueOf(lastOrderNumber.split("-")[1])+1);
		}else{
			orderNumber=orderNumber+"-1";
		}
		SplitOrderProductionTaskDTO dto = new SplitOrderProductionTaskDTO();
		dto.setIsSplit(DicConstant.CommonDic.DEFAULT_ZERO);
		dto.setWorkOrderNumber(order.getWorkOrderNumber());
		dto.setInspectionQuantity(productionOrderDTO.getOldQquantity());
		splitList.add(dto);
		for(Integer num:productionOrderDTO.getSplitQuantity()){
			ProductionOrder newOrder = new ProductionOrder();
			BeanUtils.copyProperties(order,newOrder);
			newOrder.setId(null);
			newOrder.setQuantity(num);
			newOrder.setWorkOrderNumber(orderNumber);
			awaitingProductionOrderMapper.insert(newOrder);
			orderNumber=orderNumber.split("-")[0]+(Integer.valueOf(orderNumber.split("-")[1])+1);
			//复制保存原工单绑定的方案
			CustomerProcessScheme  customerProcessScheme = customerProcessSchemeMapper.selectByProductionOrder(order.getId());
			QueryWrapper wrapper1 = new QueryWrapper();
			wrapper1.eq("CODEX_TORCH_MASTER_FORM_ID",customerProcessScheme.getId());
			List<CustomerExperimentProject> listproject = customerExperimentProjectMapper.selectList(wrapper1);
			CustomerProcessScheme newScheme = new CustomerProcessScheme();
			BeanUtils.copyProperties(customerProcessScheme,newScheme);
			newScheme.setWorkOrder(orderNumber);
			newScheme.setId(null);
			customerProcessSchemeMapper.insert(newScheme);
			listproject.stream().forEach(x->{
				CustomerExperimentProject newproject = new CustomerExperimentProject();
				BeanUtils.copyProperties(x,newproject);
				newproject.setId(null);
				newproject.setCodexTorchMasterFormId(newScheme.getId());
				customerExperimentProjectMapper.insert(newproject);
				wrapper1.clear();
				wrapper1.eq("CODEX_TORCH_MASTER_FORM_ID",x.getId());
				List<CustomerExperimentProjectData> listprojectData =customerExperimentProjectDataMapper.selectList(wrapper1);
				listprojectData.stream().forEach(s->{
					CustomerExperimentProjectData newprojectdata = new CustomerExperimentProjectData();
					BeanUtils.copyProperties(s,newprojectdata);
					newprojectdata.setId(null);
					newprojectdata.setCodexTorchMasterFormId(newproject.getId());
					customerExperimentProjectDataMapper.insert(newprojectdata);
				});
			});

			if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PROGRESS)){
				//调用工单任务分单接口拆分工单任务
				SplitOrderProductionTaskDTO newdto = new SplitOrderProductionTaskDTO();
				newdto.setIsSplit(DicConstant.CommonDic.DEFAULT_ONE);
				newdto.setWorkOrderNumber(orderNumber);
				newdto.setInspectionQuantity(num);
				splitList.add(newdto);
			}
		}
		awaitingProductionOrderMapper.updateById(order);
		productionTaskService.splitOrder(splitList);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("分单");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse cancelPdaWarning(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		order.setWmfcnr(DicConstant.CommonDic.DEFAULT_ZERO);
		awaitingProductionOrderMapper.updateById(order);//保存操作历史
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse pauseProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(!order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PROGRESS))
			throw new ServiceException("订单状态不是进行中不能进行暂停操作");
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PAUSE);
		awaitingProductionOrderMapper.updateById(order);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("暂停");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse cancelProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_CANCEL)||
		   order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_COMPLETE)||
		   order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_OUTSOURCED)||
		   order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_WAITAPPROVE)
		)
			throw new ServiceException("订单处于取消、完成、已外协、待审批状态不能进行取消操作");
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_CANCEL);
		awaitingProductionOrderMapper.updateById(order);
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("取消");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse productionOrderInstore(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		ProductList productList = productListMapper.selectById(order.getProduct());
		EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(productList.getEvaluationOrderId());
		CustomerInformationManagement customerInformationManagement= customerInformationManagementMapper.selectById(evaluationOrder.getCustomerId());
		order.setWhetherToEnterComponents(productionOrderDTO.getWhetherToEnterComponents());
		order.setWhetherToEnterDocuments(productionOrderDTO.getWhetherToEnterDocuments());
		//TODO 调用入库接口保存入库数据，组装入库DTO
		ProductInventoryDTO dto = new ProductInventoryDTO();
		dto.setProductModel(productList.getProductModel());
		dto.setWorkOrderNumber(order.getWorkOrderNumber());
		dto.setProductName(productList.getProductName());
		dto.setEntrustedUnit(customerInformationManagement.getEntrustedUnit());
		dto.setBatch(productList.getProductionBatch());
		dto.setQuantityOfCommissionedItems(productList.getSampleTotalCount());
		dto.setQuantityOfScreenedItems(order.getQuantity());

		dto.setEngineeringCode(evaluationOrder.getEngineeringCode());
		dto.setInspectionSender(evaluationOrder.getPrincipal());
		if(productionOrderDTO.getWhetherToEnterDocuments().equals(DicConstant.CommonDic.DEFAULT_ONE)){
			dto.setReport(DicConstant.CommonDic.DEFAULT_ONE);
			//TODO 根据工单查询报告编号
//			dto.setReportNumber();
			ProductionOrderResultVO resultVO = productionOrderResultService.selectResultByProductionOrder(order.getId());
			if(!ObjectUtils.isEmpty(resultVO))
			 	dto.setUnqualifiedQuantity(resultVO.getUnqualifiedQuantity());//不合格数量
			TorchResponse<List<UnqualifiedProcessVO>> response = productionTaskService.getUnqualifiedTaskInfo(order.getWorkOrderNumber());
			TorchResponse<Long> qualifiedQuantiryResponse =  productionTaskService.getLastProcessQualifiedQuantity(order.getWorkOrderNumber());
			if(!ObjectUtils.isEmpty(response.getData().getData())){
				String process = String.join(",",response.getData().getData().stream().map(UnqualifiedProcessVO::getProcessName).collect(Collectors.toList()));
				String reson = String.join(",",response.getData().getData().stream().map(UnqualifiedProcessVO::getFailureMode).collect(Collectors.toList()));
				dto.setNonQualityProcess(process);
				dto.setReasonForNonQuality(reson);
			}
			if(!ObjectUtils.isEmpty(qualifiedQuantiryResponse.getData().getData()))
				dto.setQualifiedQuantity(qualifiedQuantiryResponse.getData().getData().intValue());

		}

		awaitingProductionOrderMapper.updateById(order);
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("入库");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse connotScreened(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		order.setIrretrievableReason(productionOrderDTO.getIrretrievableReason());
		order.setNonAgingReason(productionOrderDTO.getNonAgingReason());
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_CANCEL);
		order.setTestsMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_CANNOT_SCREENED);
		awaitingProductionOrderMapper.updateById(order);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse outSorucingBack(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(productionOrderDTO.getOutSourcingStatus().equals(DicConstant.OutSourcingStatus.OUTSOURCING_REJECT)){
			order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PAUSE);
		}
		if(productionOrderDTO.getOutSourcingStatus().equals(DicConstant.OutSourcingStatus.OUTSOURCING_APPROVED)){
			order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_OUTSOURCED);
		}
		if(productionOrderDTO.getOutSourcingStatus().equals(DicConstant.OutSourcingStatus.OUTSOURCING_ACCEPTED)){
			if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED)){
				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED);
			}
			if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT)){
				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_COMPLETE);
			}
		}
		awaitingProductionOrderMapper.updateById(order);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse outSourcingApply(AddOrUpdateOutsourcingDTO addOrUpdateOutsourcingDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(addOrUpdateOutsourcingDTO.getOrderId());
		if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT)||
				order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED)
		)
			throw new ServiceException("订单不是草稿或审批通过状态不能进行外协申请");
		addOrUpdateOutsourcingDTO.setEntireOrProcess(DicConstant.CommonDic.DEFAULT_ZERO);
		outsourcingService.saveOrUpdate(addOrUpdateOutsourcingDTO);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(addOrUpdateOutsourcingDTO.getOrderId());
		history.setOperate("外协申请");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}


	@Override
	public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
	    Map<String, String> data = new HashMap();
        try {
	        switch (linkageDataTableName) {
                case "evaluation_order":
                    data = selectDataLinkageByOrderNumber(conditionalValue);
                    break;
                case "product_list1":
                    data = selectDataLinkageByProductModel(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
	    TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
	}
    @Override
    public Map<String,String> selectDataLinkageByOrderNumber(String order_number) {
        return awaitingProductionOrderMapper.selectDataLinkageByOrderNumber(order_number);
    }
    @Override
    public Map<String,String> selectDataLinkageByProductModel(String product_model) {
        return awaitingProductionOrderMapper.selectDataLinkageByProductModel(product_model);
    }

    @Override
    @ExcelExportConversion(tableName = "production_order", convertorFields = "reportRequirements,reportFormat,dataReqERep,dataReqsPapereport,testMethodology,whetherToIncludeInScheduling,workOrderStatus,wtstabr,whetherToEnterComponents,whetherToEnterDocuments,wmfcnr")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductionOrderVO> selectAwaitingProductionOrderList(ProductionOrderDTO dto) {
        return awaitingProductionOrderMapper.selectAwaitingProductionOrderList(dto);
    }

    @Override
    public TorchResponse selectAwaitingProductionOrderListByIds(List<String> ids) {
        List<ProductionOrderVO> awaitingProductionOrderList = awaitingProductionOrderMapper.selectAwaitingProductionOrderListByIds(ids);

		TorchResponse<List<ProductionOrderVO>> response = new TorchResponse<List<ProductionOrderVO>>();
		response.getData().setData(awaitingProductionOrderList);
		response.setStatus(200);
		response.getData().setCount((long)awaitingProductionOrderList.size());
		return response;
    }

	@Override
	public TorchResponse issuedProductByIds(List<String> ids) {
		//根据产品列表id查询产品信息
		List<ProductListVO> productList = productListMapper.selectProductListListByIds(ids);
		Map<String,String> workordernumMap = new HashMap<>();
		for(ProductListVO vo : productList){
			String workOrder = "";
			//判断下发的数据中是否有同型号同批次
			workordernumMap.put(vo.getProductModel()+vo.getProductionBatch(),"");
			workOrder =this.getProductionNumber(vo,workordernumMap);
			//查询测评订单信息
			EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(vo.getEvaluationOrderId());
			ProductionOrder productionOrder = new ProductionOrder();
			productionOrder.setWorkOrderNumber(workOrder);
			productionOrder.setOrderNumber(evaluationOrder.getOrderNumber());
			productionOrder.setProduct(vo.getId());
			productionOrder.setTestsMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_SELF);
			productionOrder.setQuantity(vo.getQuantity());
			productionOrder.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT);
			productionOrder.setProductionStage(DicConstant.ProductionOrder.PRODUCTION_STAGE_NOSTARTED);
			productionOrder.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
			productionOrder.setCodexTorchGroupId(vo.getCodexTorchGroupId());
			productionOrder.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.insert(productionOrder);
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse setResponsiblePerson(ProductionOrderDTO awaitingProductionOrderDto) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(awaitingProductionOrderDto.getId());
		order.setResponsiblePerson(awaitingProductionOrderDto.getResponsiblePerson());
		ProductionOrderOperationHistory productionOrderOperationHistory = new ProductionOrderOperationHistory();
		productionOrderOperationHistory.setProductionOrder(order.getId());
		productionOrderOperationHistory.setOperate("指派负责人");
		productionOrderOperationHistory.setOperator(SecurityContextHolder.getCurrentUserId());
		productionOrderOperationHistory.setOperateTime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		productionOrderOperationHistoryMapper.insert(productionOrderOperationHistory);
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse judgeModelAndBatch(ProductionOrderDTO awaitingProductionOrderDto) {
		SysGroup groupKekao = sysGroupService.findGroupsByCode(DicConstant.Group.GROUP_KEKAOXING);
		//查询当前工单
		ProductionOrder order = awaitingProductionOrderMapper.selectById(awaitingProductionOrderDto.getId());
		ProductList productList = productListMapper.selectById(order.getProduct());
		if(!order.getCodexTorchGroupId().equals(groupKekao.getId())){
			TorchResponse response = new TorchResponse<>();
			response.setStatus(Constant.REQUEST_SUCCESS);
			return response;
		}
		List<ProductionOrderVO> existOrder = awaitingProductionOrderMapper.selectSameModelBatchManufacture(productList.getProductModel(),
				productList.getProductionBatch(),productList.getManufacturer(),order.getCodexTorchGroupId(), awaitingProductionOrderDto.getId());
		String flag=DicConstant.CommonDic.DEFAULT_ZERO;
		if(existOrder.size()>1)flag=DicConstant.CommonDic.DEFAULT_ONE;
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(flag);
		return response;
	}

	@Override
	public TorchResponse SaveWtstabr(ProductionOrderDTO dto) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(dto.getId());
		order.setWtstabr(dto.getWtstabr());
		awaitingProductionOrderMapper.updateById(order);
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse saveCustomerProcessSheme(CustomerProcessSchemeDTO customerProcessSchemeDTO, String token) {
		//查询工单信息
		ProductionOrder order= awaitingProductionOrderMapper.selectById(customerProcessSchemeDTO.getWorkOrder());
		//如果是提交,需要判断
		if(customerProcessSchemeDTO.getCommitType().equals(DicConstant.CommonDic.DEFAULT_ONE)){
			boolean result = capabilityReviewService.isReviewOkByPrioductListId(order.getProduct());
			if(!result){//评审未通过
				TorchResponse response = new TorchResponse<>();
				response.setStatus(Constant.REQUEST_SUCCESS);
				response.setMessage("能力评审未通过,不能提交");
				return response;
			}
			//提交审批
			ProductionOrderDTO dto = new ProductionOrderDTO();
			BeanUtils.copyProperties(order,dto);
			this.apply(dto,token);
			order.setCodexTorchApplicant(SecurityContextHolder.getCurrentUserId());//保存申请人
			awaitingProductionOrderMapper.updateById(order);
		}

		ProductList productList =productListMapper.selectById(order.getProduct());
		EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(productList.getEvaluationOrderId());

		//判断工单是否已绑定方案如果已绑定，将原有方案删除新增
		CustomerProcessScheme scheme =customerProcessSchemeMapper.selectByProductionOrder(customerProcessSchemeDTO.getWorkOrder());
		if(ObjectUtils.isEmpty(scheme)){
			scheme = new CustomerProcessScheme();
			scheme.setWorkOrder(customerProcessSchemeDTO.getWorkOrder());
			scheme.setTestType(productList.getTestType());
			scheme.setEntrustedUnit(evaluationOrder.getCustomerId());
			scheme.setStandardSpecificationNumber(productList.getStandardSpecificationId());
			scheme.setManufacturer(productList.getManufacturer());
			scheme.setQualityGrade(productList.getQualityGrade());
			scheme.setPda(order.getPda());
			scheme.setStatus(DicConstant.CommonDic.DIC_NO);
			//查询产品管理id
			QueryWrapper wrapper = new QueryWrapper();
			wrapper.eq("product_model",productList.getProductModel());
			wrapper.eq("product_name",productList.getProductName());
			wrapper.eq("manufacturer",productList.getManufacturer());
			ProductManagement productManagement =productManagementMapper.selectOne(wrapper);
			if(!ObjectUtils.isEmpty(productManagement))
				scheme.setProductModel(productManagement.getId());
			scheme.setPackageForm(order.getPackageForm());

//		scheme.setTestPackage(productList.gettes);
			scheme.setComment(customerProcessSchemeDTO.getComment());
			scheme.setReportComment(customerProcessSchemeDTO.getReportComment());
			scheme.setDepartment(order.getCodexTorchGroupId());
			scheme.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
			scheme.setCodexTorchCreatorId(order.getCodexTorchGroupId());
			scheme.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
			customerProcessSchemeMapper.insert(scheme);
		}else{
			scheme.setComment(customerProcessSchemeDTO.getComment());
			scheme.setReportComment(customerProcessSchemeDTO.getReportComment());
			customerProcessSchemeMapper.updateById(scheme);
			QueryWrapper wrapper = new QueryWrapper();
			wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",scheme.getId());
			List<CustomerExperimentProject> projects= customerExperimentProjectMapper.selectList(wrapper);
			wrapper.clear();
			List<String> ids = projects.stream().map(CustomerExperimentProject::getId).collect(Collectors.toList());
			wrapper.in("CODEX_TORCH_MASTER_FORM_ID",ids);
			customerExperimentProjectDataMapper.delete(wrapper);
			wrapper.clear();
			wrapper.in("id",ids);
			customerExperimentProjectMapper.delete(wrapper);
		}
		//保存试验项目
		for(CustomerExperimentProjectDTO dto:customerProcessSchemeDTO.getDetailFormItems()){
			CustomerExperimentProject customerExperimentProject = new CustomerExperimentProject();
			BeanUtils.copyProperties(dto,customerExperimentProject);
			customerExperimentProject.setId(null);
			//非必要字段处理
			customerExperimentProject.setCodexTorchDeleted(Constant.DEFAULT_NO);
			//主子表关联ID
			customerExperimentProject.setCodexTorchMasterFormId(scheme.getId());
			customerExperimentProjectMapper.insert(customerExperimentProject);
			CustomerExperimentProjectDataDTO[] list = dto.getProjectDataItems();
			if(null!=list){
				for (CustomerExperimentProjectDataDTO dataDTO : dto.getProjectDataItems()){
					CustomerExperimentProjectData customerExperimentProjectData = new CustomerExperimentProjectData();
					BeanUtils.copyProperties(dataDTO,customerExperimentProjectData);
					customerExperimentProjectData.setId(null);
					customerExperimentProjectData.setCodexTorchDeleted(Constant.DEFAULT_NO);
					//主子表关联ID
					customerExperimentProjectData.setCodexTorchMasterFormId(customerExperimentProject.getId());
					customerExperimentProjectDataMapper.insert(customerExperimentProjectData);
				}
			}
		}
		ProductionOrderOperationHistory productionOrderOperationHistory = new ProductionOrderOperationHistory();
		productionOrderOperationHistory.setProductionOrder(order.getId());
		productionOrderOperationHistory.setOperate("绑定工序方案");
		productionOrderOperationHistory.setOperator(SecurityContextHolder.getCurrentUserId());
		productionOrderOperationHistory.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(productionOrderOperationHistory);

		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}
	private String getProductionNumber(ProductListVO vo, Map<String, String> workordernumMap){
		SysGroup groupKekao = sysGroupService.findGroupsByCode(DicConstant.Group.GROUP_KEKAOXING);
		SysGroup groupShengchan = sysGroupService.findGroupsByCode(DicConstant.Group.GROUP_SHENGCHAN);
		String workOrder="";
		//查询工单中是否有同型号同批次的工单，如果有则工单编号为原工单加上“-N”
		ProductionOrder oldproductionOrder = awaitingProductionOrderMapper.selectProductionOrderByModelAndBatch(vo.getEvaluationOrderId(),vo.getProductModel(),vo.getProductionBatch());
		if(!ObjectUtils.isEmpty(oldproductionOrder))
			workordernumMap.put(vo.getProductModel()+vo.getProductionBatch(),oldproductionOrder.getWorkOrderNumber());
		if(workordernumMap.get(vo.getProductModel()+vo.getProductionBatch()).equals("")){
			//根据试验类型生成工单号-K
			if(vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_DPA)||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_SPECIAL_ANALYSIS)
					||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_FAILURE_ANALYSIS)
					||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST)
					||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)
					||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_OTHER_TEST)){
				TorchResponse response = codeManagementService.getOrderNumber("K");
				workOrder=response.getData().getData().toString();
				vo.setCodexTorchGroupId(groupKekao.getId());
			}
			//根据试验类型生成工单号-S
			if(vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_ONE)||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_TWO)
					||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_ENVIRONMENT)
					||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_REINSPECTION)
					||vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_MICROWAVE_QUALIFICATION_TEST)){
				TorchResponse response = codeManagementService.getOrderNumber("S");
				workOrder=response.getData().getData().toString();
				vo.setCodexTorchGroupId(groupShengchan.getId());
			}
			workordernumMap.put(vo.getProductModel()+vo.getProductionBatch(),workOrder);
		}else{
			String[] nums = workordernumMap.get(vo.getProductModel()+vo.getProductionBatch()).split("-");
			if(nums.length==1){
				workOrder=nums[0]+"-1";
			}
			if(nums.length>1){
				workOrder=nums[0]+String.valueOf(Integer.valueOf(nums[1])+1);
			}
			workordernumMap.put(vo.getProductModel()+vo.getProductionBatch(),workOrder);
		}
		return workOrder;
	}


}