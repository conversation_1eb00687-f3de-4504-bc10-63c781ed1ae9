package com.huatek.frame.modules.business.rest;

import cn.hutool.core.lang.UUID;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.EvaluationOrderQueryRespVO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.business.domain.EvaluationOrder;
import com.huatek.frame.modules.business.service.EvaluationOrderService;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderDTO;
import com.huatek.frame.modules.business.domain.vo.EvaluationOrderVO;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-07-30
**/
@Api(tags = "测评订单管理")
@RestController
@RequestMapping("/api/evaluationOrder")
public class EvaluationOrderController {

	@Autowired
    private EvaluationOrderService evaluationOrderService;

	/**
	 * 测评订单列表
	 * 
	 * @param dto 测评订单DTO 实体对象
	 * @return
	 */
    @Log("测评订单列表")
    @ApiOperation(value = "测评订单列表查询")
    @PostMapping(value = "/evaluationOrderList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("evaluationOrder:list")
    public TorchResponse<List<EvaluationOrderVO>> query(@RequestBody EvaluationOrderDTO dto){
        return evaluationOrderService.findEvaluationOrderPage(dto);
    }

	/**
	 * 新增/修改测评订单
	 * 
	 * @param evaluationOrderDto 测评订单DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改测评订单")
    @ApiOperation(value = "测评订单新增/修改操作")
    @PostMapping(value = "/evaluationOrder", produces = { "application/json;charset=utf-8" })
    @TorchPerm("evaluationOrder:add#evaluationOrder:edit")
    public TorchResponse add(@RequestBody EvaluationOrderDTO evaluationOrderDto) throws Exception {
		return evaluationOrderService.saveOrUpdate(evaluationOrderDto);
	}

	/**
	 * 查询测评订单详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("测评订单详情")
    @ApiOperation(value = "测评订单详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("evaluationOrder:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return evaluationOrderService.findEvaluationOrder(id);
	}

	/**
	 * 删除测评订单
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除测评订单")
    @ApiOperation(value = "测评订单删除操作")
    @TorchPerm("evaluationOrder:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return evaluationOrderService.delete(ids);
	}

    @ApiOperation(value = "测评订单联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return evaluationOrderService.getOptionsList(id);
	}




    @ApiOperation(value = "测评订单 自动填充数据查询")
    @GetMapping(value = "/linkageData/{linkageDataTableName}/{conditionalValue}", produces = { "application/json;charset=utf-8" })
    public TorchResponse getLinkageData(@PathVariable(value = "linkageDataTableName") String linkageDataTableName,
    									@PathVariable(value = "conditionalValue") String conditionalValue) {
		return evaluationOrderService.getLinkageData(linkageDataTableName, conditionalValue);
	}

    @Log("测评订单导出")
    @ApiOperation(value = "测评订单导出")
    @TorchPerm("evaluationOrder:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody EvaluationOrderDTO dto)
    {
        List<EvaluationOrderVO> list = evaluationOrderService.selectEvaluationOrderList(dto);
        ExcelUtil<EvaluationOrderVO> util = new ExcelUtil<EvaluationOrderVO>(EvaluationOrderVO.class);
        util.exportExcel(response, list, "测评订单数据");
    }

    @Log("测评订单导入")
    @ApiOperation(value = "测评订单导入")
    @TorchPerm("evaluationOrder:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<EvaluationOrderVO> util = new ExcelUtil<EvaluationOrderVO>(EvaluationOrderVO.class);
        List<EvaluationOrderVO> list = util.importExcel(file.getInputStream());
        return evaluationOrderService.importEvaluationOrder(list, unionColumns, true, "");
    }

    @Log("测评订单导入模板")
    @ApiOperation(value = "测评订单导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<EvaluationOrderVO> util = new ExcelUtil<EvaluationOrderVO>(EvaluationOrderVO.class);
        util.importTemplateExcel(response, "测评订单数据");
    }

    @Log("根据Ids获取测评订单列表")
    @ApiOperation(value = "测评订单 根据Ids批量查询")
    @PostMapping(value = "/evaluationOrderList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getEvaluationOrderListByIds(@RequestBody List<String> ids) {
        return evaluationOrderService.selectEvaluationOrderListByIds(ids);
    }

    /**
     * 测评订单主子表单组合提交
     *
	 * @param evaluationOrderDto 测评订单DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Log("测评订单主子表单组合提交")
    @ApiOperation(value = "测评订单主子表单组合提交")
    //@TorchPerm("evaluationOrder:masterDetailSubmit")
    @PostMapping(value = "/masterDetailSubmit", produces = {"application/json;charset=utf-8"})
    public TorchResponse submitMasterDetails(@RequestBody EvaluationOrderDTO evaluationOrderDto) {
        return evaluationOrderService.submitMasterDetails(evaluationOrderDto);
    }

    @Log("生成id")
    @ApiOperation(value = "生成id")
    @PostMapping(value = "/generateId", produces = {"application/json;charset=utf-8"})
    public TorchResponse generateId(){
        String uuid = UUID.randomUUID().toString().replace("-", "");
        TorchResponse response = new TorchResponse();
        response.getData().setData(uuid);
        return response;
    }

}