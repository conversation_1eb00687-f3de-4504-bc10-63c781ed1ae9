package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.modules.business.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 设备台账DTO 实体类
* <AUTHOR>
* @date 2025-07-18
**/
@Data
@ApiModel("设备台账DTO实体类")
public class EquipmentInventoryDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 设备编号
     **/
    @ApiModelProperty("设备编号")
    private String deviceSerialNumber;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;
    
    /**
	 * 资产编号
     **/
    @ApiModelProperty("资产编号")
    private String assetNumber;
    
    /**
	 * 是否金蝶同步
     **/
    @ApiModelProperty("是否金蝶同步")
    private String d0;
    
    /**
	 * 出厂编号
     **/
    @ApiModelProperty("出厂编号")
    private String d03;
    
    /**
	 * 规格型号
     **/
    @ApiModelProperty("规格型号")
    private String specificationModel;
    
    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    private String manufacturer;
    

    
    /**
	 * 设备类型
     **/
    @ApiModelProperty("设备类型")
    private String deviceType;

    /**
     * 设备能耗
     */
    /**
     * 设备能耗
     */
    @ApiModelProperty("设备能耗")
    private String deviceEnergyConsumption;

    /**
     * 设备类型编码
     **/
    @ApiModelProperty("设备类型编码")
    private String deviceTypeCode;
    
    /**
	 * 技术指标
     **/
    @ApiModelProperty("技术指标")
    private String technicalSpecifications;
    
    /**
	 * 设备分类
     **/
    @ApiModelProperty("设备分类")
    private String deviceCategory;
    
    /**
	 * 启用时间
     **/
    @ApiModelProperty("启用时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date enableTime;
    
    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    private String status;
    
    /**
	 * 所属分组
     **/
    @ApiModelProperty("所属分组")
    private String belongingGroup;


    /**
	 * 负责人
     **/
    @ApiModelProperty("负责人")
    private String responsiblePerson;
    
    /**
	 * 摆放位置
     **/
    @ApiModelProperty("摆放位置")
    private String positioning;
    
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    private String comment;
    
    /**
	 * 验收报告
     **/
    @ApiModelProperty("验收报告")
    private String acceptanceReport;
    
    /**
	 * 上传附件
     **/
    @ApiModelProperty("上传附件")
    private String uploadAttachment;
    
    /**
	 * 子表明细项ID
     **/
    @ApiModelProperty("子表明细项ID")
    private String codexTorchDetailItemIds;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


    /**
     * 子表明细项
     */
    @ApiModelProperty("子表明细项")
    private TraceInformationDTO[] detailFormItems;

	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;

}