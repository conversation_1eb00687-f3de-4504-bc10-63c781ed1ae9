-- 为 production_task 表添加新字段的 MySQL 语句

-- 添加客户工序名称字段
ALTER TABLE production_task 
ADD COLUMN customer_process_name VARCHAR(255) COMMENT '客户工序名称';

-- 添加显示序号字段
ALTER TABLE production_task 
ADD COLUMN display_number INT(11) COMMENT '显示序号';

-- 可选：为新字段添加索引（如果需要经常按这些字段查询）
-- CREATE INDEX idx_production_task_customer_process_name ON production_task(customer_process_name);
-- CREATE INDEX idx_production_task_display_number ON production_task(display_number);

-- 验证字段是否添加成功
-- DESCRIBE production_task;
