package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;

import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;

import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.system.domain.SysGroup;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
//import com.huatek.frame.modules.system.mapper.SysGroupMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.Workstation;
import com.huatek.frame.modules.business.domain.vo.WorkstationVO;
import com.huatek.frame.modules.business.mapper.WorkstationMapper;
import com.huatek.frame.modules.business.service.WorkstationService;
import com.huatek.frame.modules.business.service.dto.WorkstationDTO;

import com.huatek.frame.modules.business.domain.DeviceType;
import com.huatek.frame.modules.business.mapper.DeviceTypeMapper;
import org.springframework.util.CollectionUtils;



/**
 * 工作站 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "workstation")
//@RefreshScope
@Slf4j
public class WorkstationServiceImpl implements WorkstationService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private WorkstationMapper workstationMapper;

//	@Autowired
//    private SysGroupMapper sysGroupMapper;
	@Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    protected Validator validator;

    @Autowired
    private CodeManagementService codeManagementService;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public WorkstationServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<WorkstationVO>> findWorkstationPage(WorkstationDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<WorkstationVO> workstations = workstationMapper.selectWorkstationPage(dto);
		TorchResponse<List<WorkstationVO>> response = new TorchResponse<List<WorkstationVO>>();
		response.getData().setData(workstations);
		response.setStatus(200);
		response.getData().setCount(workstations.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(WorkstationDTO workstationDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(workstationDto.getCodexTorchDeleted())) {
            workstationDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = workstationDto.getId();
		Workstation entity = new Workstation();
        BeanUtils.copyProperties(workstationDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
            TorchResponse response =  codeManagementService.getOrderNumber("GZZ");
            entity.setWorkstationNumber(response.getData().getData().toString());
			workstationMapper.insert(entity);
		} else {
			workstationMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        WorkstationVO vo = new WorkstationVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<WorkstationVO> findWorkstation(String id) {
		WorkstationVO vo = new WorkstationVO();
		if (!HuatekTools.isEmpty(id)) {
			Workstation entity = workstationMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<WorkstationVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		workstationMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("groupId",workstationMapper::selectOptionsByDepartment);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("deviceType",workstationMapper::selectOptionsByDeviceType);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "workstation", convertorFields = "status")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<WorkstationVO> selectWorkstationList(WorkstationDTO dto) {
        return workstationMapper.selectWorkstationList(dto);
    }

    /**
     * 导入工作站数据
     *
     * @param workstationList 工作站数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "workstation", convertorFields = "status")
    public TorchResponse importWorkstation(List<WorkstationVO> workstationList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(workstationList) || workstationList.size() == 0) {
            throw new ServiceException("导入工作站数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (WorkstationVO vo : workstationList) {
            try {
                Workstation workstation = new Workstation();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, workstation);
                QueryWrapper<Workstation> wrapper = new QueryWrapper();
                Workstation oldWorkstation = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = WorkstationVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<Workstation> oldWorkstationList = workstationMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldWorkstationList) && oldWorkstationList.size() > 1) {
                        workstationMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldWorkstationList) && oldWorkstationList.size() == 1) {
                        oldWorkstation = oldWorkstationList.get(0);
                    }
                }
                if (StringUtils.isNull(oldWorkstation)) {
                    BeanValidators.validateWithException(validator, vo);
                    workstationMapper.insert(workstation);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工作站编号 " + vo.getWorkstationNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldWorkstation, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    workstationMapper.updateById(oldWorkstation);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工作站编号 " + vo.getWorkstationNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、工作站编号 " + vo.getWorkstationNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、工作站编号 " + vo.getWorkstationNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(WorkstationVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getWorkstationNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>工作站编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getWorkstationName())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>工作站名称不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getDepartment())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>所属部门不能为空!");
        }
//        if (!HuatekTools.isEmpty(vo.getDepartment())) {
//            List<String> departmentList = Arrays.asList(vo.getDepartment().split(","));
//            List<SysGroup> list = sysGroupMapper.selectList(new QueryWrapper<SysGroup>().in("group_name", departmentList));
//            if (CollectionUtils.isEmpty(list)) {
//                failureRecord++;
//                failureRecordMsg.append("所属部门=" + vo.getDepartment() + "; ");
//            }
//        }
        if (HuatekTools.isEmpty(vo.getStatus())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>状态不能为空!");
        }
        if (!HuatekTools.isEmpty(vo.getDeviceType())) {
            List<String> deviceTypeList = Arrays.asList(vo.getDeviceType().split(","));
            List<DeviceType> list = deviceTypeMapper.selectList(new QueryWrapper<DeviceType>().in("device_type_name", deviceTypeList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("设备类型=" + vo.getDeviceType() + "; ");
            }
        }
        if (HuatekTools.isEmpty(vo.getCodexTorchUpdater())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>更新人不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectWorkstationListByIds(List<String> ids) {
        List<WorkstationVO> workstationList = workstationMapper.selectWorkstationListByIds(ids);

		TorchResponse<List<WorkstationVO>> response = new TorchResponse<List<WorkstationVO>>();
		response.getData().setData(workstationList);
		response.setStatus(200);
		response.getData().setCount((long)workstationList.size());
		return response;
    }

	@Override
	public TorchResponse<WorkstationVO> findWorkstationByScannerGunNumber(String scannerGunNumber) {
		if (HuatekTools.isEmpty(scannerGunNumber)) {
			throw new ServiceException("扫码枪编号不能为空");
		}

		WorkstationDTO dto = new WorkstationDTO();
		dto.setScannerGunNumber(scannerGunNumber);
		List<WorkstationVO> list = workstationMapper.selectWorkstationList(dto);

		TorchResponse<WorkstationVO> response = new TorchResponse<>();
		if (list != null && !list.isEmpty()) {
			response.getData().setData(list.get(0));
			response.setStatus(Constant.REQUEST_SUCCESS);
		} else {
			throw new ServiceException("未找到对应的工作站信息");
		}
		return response;
	}

}
