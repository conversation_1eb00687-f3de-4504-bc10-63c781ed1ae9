package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
//import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;

/**
* @description 试验项目VO实体类
* <AUTHOR>
* @date 2025-07-17
**/
@Data
@ApiModel("试验项目DTO实体类")
public class CustomerExperimentProjectVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 工序方案名称
     **/
    @ApiModelProperty("工序方案名称")
    @Excel(name = "工序方案名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String processSchemeName;

    /**
     * 显示序号
     **/
    @ApiModelProperty("显示序号")
    @Excel(name = "显示序号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer displayNumber;


    /**
     * 工序编码
     **/
    @ApiModelProperty("工序编码")
    @Excel(name = "工序编码",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String processCode3;
    private String processCode3Name;

    /**
     * 客户试验项目名称
     **/
    @ApiModelProperty("客户工序名称")
    @Excel(name = "客户工序名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String customerProcessName;

    /**
     * 执行顺序
     **/
    @ApiModelProperty("执行顺序")
    @Excel(name = "执行顺序",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer executionSequence;

    /**
     * 关联工单前置工序
     **/
    @ApiModelProperty("关联工单前置工序")
    @Excel(name = "关联工单前置工序",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String assoWoPredProc;
    private String assoWoPredProcName;

    /**
     * 试验条件
     **/
    @ApiModelProperty("试验条件")
    @Excel(name = "试验条件",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testConditions;

    /**
     * 试验依据
     **/
    @ApiModelProperty("试验依据")
    @Excel(name = "试验依据",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testBasis;

    /**
     * 试验次数
     **/
    @ApiModelProperty("试验次数")
    @Excel(name = "试验次数",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testingTimes;

    /**
     * 试验时长
     **/
    @ApiModelProperty("试验时长")
    @Excel(name = "试验时长",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Long durationOfTesting;

    /**
     * 判定依据
     **/
    @ApiModelProperty("判定依据")
    @Excel(name = "判定依据",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String judgmentCriteria;


    /**
     * 工作站
     **/
    @ApiModelProperty("工作站")
    @Excel(name = "工作站",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workstation;
    private String workstationName;

    /**
     * 设备类型
     **/
    @ApiModelProperty("设备类型")
    @Excel(name = "设备类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String deviceType;

    /**
     * 产品资料
     **/
    @ApiModelProperty("产品资料")
    @Excel(name = "产品资料",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productInformation1;
    private String productInformation1Name;

    /**
     * 试验方式
     **/
    @ApiModelProperty("试验方式")
    @Excel(name = "试验方式",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testMethodology;

    /**
     * 试验班组
     **/
    @ApiModelProperty("试验班组")
    @Excel(name = "试验班组",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testingTeam;
    private String testingTeamName;

    /**
     * PDA
     **/
    @ApiModelProperty("PDA")
    @Excel(name = "PDA",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Long pda;

    /**
     * 组别
     **/
    @ApiModelProperty("组别")
    @Excel(name = "组别",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String grouping;
    private String groupingName;

    /**
     * 样品数量
     **/
    @ApiModelProperty("样品数量")
    @Excel(name = "样品数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String sampleQuantity12;

    /**
     * 是否加入排产
     **/
    @ApiModelProperty("是否加入排产")
    @Excel(name = "是否加入排产",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String whetherToIncludeInScheduling;

    /**
     * 预计开始时间
     **/
    @ApiModelProperty("预计开始时间")
    private Timestamp estimatedStartTime;
    /**
     * 预计结束时间
     **/
    @ApiModelProperty("预计结束时间")
    private Timestamp estimatedEndTime;

    @ApiModelProperty("客户数据明细项")
    private List<CustomerExperimentProjectDataVO> customerProjectDataItems;

    /**
     * 主表单ID
     **/
    @ApiModelProperty("主表单ID")
    private String codexTorchMasterFormId;

    /**
     * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
     * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String codexTorchUpdater;

    /**
     * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
     * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
     * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
     * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;

    @ApiModelProperty("数据明细项")
    private List<ExperimentProjectDataVO> projectDataItems;



}