package com.huatek.frame.modules.business.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;

/**
* @description 设备台账VO实体类
* <AUTHOR>
* @date 2025-07-18
**/
@Data
@ApiModel("设备台账DTO实体类")
public class EquipmentInventoryVO implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 设备编号
     **/
    @ApiModelProperty("设备编号")
    @Excel(name = "设备编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String deviceSerialNumber;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    @Excel(name = "设备名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String deviceName;
    
    /**
	 * 资产编号
     **/
    @ApiModelProperty("资产编号")
    @Excel(name = "资产编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String assetNumber;
    
    /**
	 * 是否金蝶同步
     **/
    @ApiModelProperty("是否金蝶同步")
    @Excel(name = "是否金蝶同步",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String d0;
    
    /**
	 * 出厂编号
     **/
    @ApiModelProperty("出厂编号")
    @Excel(name = "出厂编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String d03;
    
    /**
	 * 规格型号
     **/
    @ApiModelProperty("规格型号")
    @Excel(name = "规格型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String specificationModel;
    
    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String manufacturer;


    /**
	 * 设备类型编码
     **/
    @ApiModelProperty("设备类型")
    @Excel(name = "设备类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String deviceType;

    /**
     * 设备类型
     **/
    @ApiModelProperty("设备类型编码")
    @Excel(name = "设备类型编码",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String deviceTypeCode;

    /**
     * 设备能耗
     */
    @ApiModelProperty("设备能耗")
    @Excel(name = "设备能耗",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String deviceEnergyConsumption;

    
    /**
	 * 技术指标
     **/
    @ApiModelProperty("技术指标")
    @Excel(name = "技术指标",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String technicalSpecifications;
    
    /**
	 * 设备分类
     **/
    @ApiModelProperty("设备分类")
    @Excel(name = "设备分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String deviceCategory;
    
    /**
	 * 启用时间
     **/
    @ApiModelProperty("启用时间")
    @Excel(name = "启用时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date enableTime;
    
    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String status;
    
    /**
	 * 所属分组
     **/
    @ApiModelProperty("所属分组")
    private String belongingGroup;

    /**
     * 所属分组名称
     */
    @ApiModelProperty("所属分组名称")
    @Excel(name = "所属分组",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String groupName;

    /**
	 * 负责人
     **/
    @ApiModelProperty("负责人")
    @Excel(name = "负责人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String responsiblePerson;
    
    /**
	 * 摆放位置
     **/
    @ApiModelProperty("摆放位置")
    @Excel(name = "摆放位置",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String positioning;
    
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    @Excel(name = "备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String comment;
    
    /**
	 * 验收报告
     **/
    @ApiModelProperty("验收报告")
    @Excel(name = "验收报告",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String acceptanceReport;
    
    /**
	 * 上传附件
     **/
    @ApiModelProperty("上传附件")
    @Excel(name = "上传附件",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String uploadAttachment;
    
    /**
	 * 子表明细项ID
     **/
    @ApiModelProperty("子表明细项ID")
    private String codexTorchDetailItemIds;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}