package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.Outsourcing;
import com.huatek.frame.modules.business.domain.vo.OutsourcingVO;
import com.huatek.frame.modules.business.service.dto.OutsourcingDTO;
import com.huatek.frame.modules.business.service.dto.OutsourcingPageDTO;
import com.huatek.frame.modules.business.service.dto.OutsourcingUpdateDTO;
import org.apache.ibatis.annotations.Param;

/**
* 外协申请mapper
* <AUTHOR>
* @date 2025-08-07
**/
public interface OutsourcingMapper extends BaseMapper<Outsourcing> {

     /**
	 * 外协申请分页
	 * @param requestParam
	 * @return
	 */
	Page<OutsourcingVO> selectOutsourcingApplicationPage(OutsourcingPageDTO requestParam);


    /**
     * 根据条件查询外协申请列表
     *
     * @param dto 外协申请信息
     * @return 外协申请集合信息
     */
    List<OutsourcingVO> selectOutsourcingApplicationList(OutsourcingDTO dto);

	/**
	 * 根据IDS查询外协申请列表
	 * @param ids
	 * @return
	 */
    List<OutsourcingVO> selectOutsourcingApplicationListByIds(@Param("ids") List<String> ids);


	/**
	 * 查找待审批状态下的外协工单
	 * @return
	 */
    List<OutsourcingVO> findPendingOutsourcings(OutsourcingPageDTO requestParam);

	/**
	 * 查询审批历史
	 * @return
	 */
	List<OutsourcingVO> findOutsourcingHistorys(OutsourcingPageDTO requestParam);

	/**
	 * 查看单个外协工单的详细信息
	 * @param id 外协工单id
	 * @return
	 */
	OutsourcingVO findOutsourcingDetails(@Param("id") String id);
}