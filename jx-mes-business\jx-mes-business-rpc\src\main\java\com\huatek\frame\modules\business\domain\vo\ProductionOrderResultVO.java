package com.huatek.frame.modules.business.domain.vo;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 工单检验结论
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("工单检验结论VO实体类")
public class ProductionOrderResultVO implements Serializable {


    @ApiModelProperty("主键ID")
    private String id;

    
    /**
	 * 工单
     **/
    @ApiModelProperty("工单")
    private String workOrder;

    
    /**
	 * 检验结论
     **/
    @ApiModelProperty("检验结论")
    private String inspectionConclusion;

    /**
     * 其他说明备注
     **/
    @ApiModelProperty("其他说明备注")
    private String othereExplanationRemark;

    /**
     * 不合格数量
     **/
    @ApiModelProperty("不合格数量")
    private Integer unqualifiedQuantity;
    /**
	 * 检验结果
     **/
    @ApiModelProperty("检验结果")
    private String inspectionResult;

    
    /**
	 * 检验形式
     **/
    @ApiModelProperty("检验形式")
    private String inspectionFrom;

    
    /**
	 * 检验规范
     **/
    @ApiModelProperty("检验规范")
    private String testSpecifications;

    

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;

    

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;
}