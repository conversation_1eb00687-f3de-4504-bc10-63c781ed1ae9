<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.EquipmentInventoryMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.device_serial_number as deviceSerialNumber,
		t.asset_number as assetNumber,
		t.d_0 as d0,
		t.d_03 as d03,
		t.specification_model as specificationModel,
		t.manufacturer as manufacturer,
		t.device_name as deviceName,
<!--		t.device_type as deviceType,-->
        t.device_type_code as deviceTypeCode,
		t.technical_specifications as technicalSpecifications,
		t.device_category as deviceCategory,
		t.enable_time as enableTime,
		t.status as status,
		t.belonging_group as belongingGroup,
		t.responsible_person as responsible<PERSON>erson,
		t.positioning as positioning,
		t.`comment` as `comment`,
		t.acceptance_report as acceptanceReport,
		t.upload_attachment as uploadAttachment,
		t.CODEX_TORCH_DETAIL_ITEM_IDS as codexTorchDetailItemIds,
		t.CODEX_TORCH_CREATOR_ID as codexTorchCreatorId,
		t.CODEX_TORCH_UPDATER as codexTorchUpdater,
		t.CODEX_TORCH_GROUP_ID as codexTorchGroupId,
		t.CODEX_TORCH_CREATE_DATETIME as codexTorchCreateDatetime,
		t.CODEX_TORCH_UPDATE_DATETIME as codexTorchUpdateDatetime,
		t.CODEX_TORCH_DELETED as codexTorchDeleted
	</sql>
	<select id="selectEquipmentInventoryPage" parameterType="com.huatek.frame.modules.business.service.dto.EquipmentInventoryDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.EquipmentInventoryVO">
		select
		<include refid="Base_Column_List" />, dt.device_type_name as deviceType, g.group_name as groupName
			from equipment_inventory t LEFT JOIN device_type dt ON t.device_type_code = dt.device_type_code
        LEFT JOIN sys_group g ON g.id = t.belonging_group
            <where>
                and 1=1
                <if test="deviceSerialNumber != null and deviceSerialNumber != ''">
                    and t.device_serial_number  like concat('%', #{deviceSerialNumber} ,'%')
                </if>
                <if test="assetNumber != null and assetNumber != ''">
                    and t.asset_number  like concat('%', #{assetNumber} ,'%')
                </if>
                <if test="d0 != null and d0 != ''">
                    and t.d_0  = #{d0}
                </if>
                <if test="d03 != null and d03 != ''">
                    and t.d_03  like concat('%', #{d03} ,'%')
                </if>
                <if test="specificationModel != null and specificationModel != ''">
                    and t.specification_model  like concat('%', #{specificationModel} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  = #{manufacturer}
                </if>
                <if test="deviceName != null and deviceName != ''">
                    and t.device_name  like concat('%', #{deviceName} ,'%')
                </if>
                <if test="deviceTypeCode != null and deviceTypeCode != ''">
                    and t.device_type_code  = #{deviceTypeCode}
                </if>
                <if test="technicalSpecifications != null and technicalSpecifications != ''">
                    and t.technical_specifications  like concat('%', #{technicalSpecifications} ,'%')
                </if>
                <if test="deviceCategory != null and deviceCategory != ''">
                    and t.device_category  = #{deviceCategory}
                </if>
                <if test="enableTime != null">
                    and t.enable_time  = #{enableTime}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="belongingGroup != null and belongingGroup != ''">
                    and t.belonging_group  = #{belongingGroup}
                </if>
                <if test="responsiblePerson != null and responsiblePerson != ''">
                    and t.responsible_person  like concat('%', #{responsiblePerson} ,'%')
                </if>
                <if test="positioning != null and positioning != ''">
                    and t.positioning  like concat('%', #{positioning} ,'%')
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="acceptanceReport != null and acceptanceReport != ''">
                    and t.acceptance_report  = #{acceptanceReport}
                </if>
                <if test="uploadAttachment != null and uploadAttachment != ''">
                    and t.upload_attachment  = #{uploadAttachment}
                </if>
                <if test="codexTorchDetailItemIds != null and codexTorchDetailItemIds != ''">
                    and t.CODEX_TORCH_DETAIL_ITEM_IDS  like concat('%', #{codexTorchDetailItemIds} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>
     <select id="selectOptionsByManufacturer" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.manufacturer label,
        	t.manufacturer value
        from standard_process_plan t
        WHERE t.manufacturer != ''
     </select>
     <select id="selectOptionsByDeviceType" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.device_type_name label,
        	t.device_type_code value
        from device_type t
        WHERE t.device_type_code != ''
     </select>
     <select id="selectOptionsByBelongingGroup" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.group_name label,
        	t.id value
        from sys_group t
        WHERE t.group_code != ''
     </select>

    <select id="selectEquipmentInventoryList" parameterType="com.huatek.frame.modules.business.service.dto.EquipmentInventoryDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.EquipmentInventoryVO">
		select
		<include refid="Base_Column_List" />
			from equipment_inventory t
            <where>
                and 1=1
                <if test="deviceSerialNumber != null and deviceSerialNumber != ''">
                    and t.device_serial_number  like concat('%', #{deviceSerialNumber} ,'%')
                </if>
                <if test="assetNumber != null and assetNumber != ''">
                    and t.asset_number  like concat('%', #{assetNumber} ,'%')
                </if>
                <if test="d0 != null and d0 != ''">
                    and t.d_0  = #{d0}
                </if>
                <if test="d03 != null and d03 != ''">
                    and t.d_03  like concat('%', #{d03} ,'%')
                </if>
                <if test="specificationModel != null and specificationModel != ''">
                    and t.specification_model  like concat('%', #{specificationModel} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  = #{manufacturer}
                </if>

                <if test="deviceTypeCode != null and deviceTypeCode != ''">
                    and t.device_type_code  = #{deviceTypeCode}
                </if>
                <if test="technicalSpecifications != null and technicalSpecifications != ''">
                    and t.technical_specifications  like concat('%', #{technicalSpecifications} ,'%')
                </if>
                <if test="deviceCategory != null and deviceCategory != ''">
                    and t.device_category  = #{deviceCategory}
                </if>
                <if test="enableTime != null">
                    and t.enable_time  = #{enableTime}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="belongingGroup != null and belongingGroup != ''">
                    and t.belonging_group  = #{belongingGroup}
                </if>
                <if test="responsiblePerson != null and responsiblePerson != ''">
                    and t.responsible_person  like concat('%', #{responsiblePerson} ,'%')
                </if>
                <if test="positioning != null and positioning != ''">
                    and t.positioning  like concat('%', #{positioning} ,'%')
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="acceptanceReport != null and acceptanceReport != ''">
                    and t.acceptance_report  = #{acceptanceReport}
                </if>
                <if test="uploadAttachment != null and uploadAttachment != ''">
                    and t.upload_attachment  = #{uploadAttachment}
                </if>
                <if test="codexTorchDetailItemIds != null and codexTorchDetailItemIds != ''">
                    and t.CODEX_TORCH_DETAIL_ITEM_IDS  like concat('%', #{codexTorchDetailItemIds} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectEquipmentInventoryListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.EquipmentInventoryVO">
		select
		<include refid="Base_Column_List" />
			from equipment_inventory t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
    <select id="findDeviceListByType" resultType="com.huatek.frame.modules.business.domain.vo.DeviceInfoVO">
        select
        t.id as id,
        t.specification_model as specificationModel,
        t.device_name as deviceName,
        t.manufacturer as manufacturer,
        t.technical_specifications as technicalSpecifications
        from equipment_inventory t
        where t.specification_model = #{specificationModel}
    </select>

</mapper>