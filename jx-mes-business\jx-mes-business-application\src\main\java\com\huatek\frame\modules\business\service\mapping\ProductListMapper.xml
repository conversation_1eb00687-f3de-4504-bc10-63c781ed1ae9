<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ProductListMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.evaluation_order_id as evaluationOrderId,
        t.standard_specification_id as standardSpecificationId,
        t.manufacturer as manufacturer,
        t.product_category as productCategory,
        t.product_name as productName,
		t.serial_number as serialNumber,
		t.product_model as productModel,
		t.production_batch as productionBatch,
		t.quantity as quantity,
		t.task_level as taskLevel,
		t.test_type as testType,
		t.work_order_inspection_number as workOrderInspectionNumber,
		t.quality_grade as qualityGrade,
		t.sample_total_count as sampleTotalCount,
		t.experiment_project as experimentProject,
		t.special_analysis_test_project as specialAnalysisTestProject,
		t.group_type as groupType,
		t.inspection_test_project as inspectionTestProject,
		t.quality_consistency_test_items as qualityConsistencyTestItems,
		t.dpa_test_project as dpaTestProject,
		t.other_tests as otherTests,
		t.technical_liaison_name as technicalLiaisonName,
		t.technical_liaison_phone_number as technicalLiaisonPhoneNumber,
		t.deadline as deadline,
		t.status as status,
		t.reject_unscreenable_reason as rejectUnscreenableReason,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
    <update id="batchUpdateProductList">
        <foreach collection="list" item="item" separator=";">
            UPDATE product_list t
            SET
            t.evaluation_order_id = #{item.evaluationOrderId},
            t.standard_specification_id = #{item.standardSpecificationId},
            t.product_model = #{item.productModel},
            t.manufacturer = #{item.manufacturer},
            t.product_category = #{item.productCategory},
            t.product_name = #{item.productName},
            t.serial_number = #{item.serialNumber},
            t.product_model = #{item.productModel},
            t.production_batch = #{item.productionBatch},
            t.quantity = #{item.quantity},
            t.task_level = #{item.taskLevel},
            t.test_type = #{item.testType},
            t.work_order_inspection_number = #{item.workOrderInspectionNumber},
            t.quality_grade = #{item.qualityGrade},
            t.sample_total_count = #{item.sampleTotalCount},
            t.experiment_project = #{item.experimentProject},
            t.special_analysis_test_project = #{item.specialAnalysisTestProject},
            t.group_type = #{item.groupType},
            t.inspection_test_project = #{item.inspectionTestProject},
            t.quality_consistency_test_items = #{item.qualityConsistencyTestItems},
            t.dpa_test_project = #{item.dpaTestProject},
            t.other_tests = #{item.otherTests},
            t.technical_liaison_name = #{item.technicalLiaisonName},
            t.technical_liaison_phone_number = #{item.technicalLiaisonPhoneNumber},
            t.deadline = #{item.deadline},
            t.status = #{item.status},
            t.reject_unscreenable_reason = #{item.rejectUnscreenableReason},
            t.codex_torch_deleted = #{item.codexTorchDeleted}
            WHERE t.id = #{item.id}
        </foreach>
    </update>
    <select id="selectProductListPage" parameterType="com.huatek.frame.modules.business.service.dto.ProductListDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductListVO">
		select
		<include refid="Base_Column_List" />,
        GROUP_CONCAT(s.specification_number) as specificationNumber
			from product_list t
        LEFT JOIN standard_specification s ON
        FIND_IN_SET(s.id, t.standard_specification_id) > 0
        OR s.id = t.standard_specification_id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="evaluationOrderId != null and evaluationOrderId != ''">
                    and t.evaluation_order_id = #{evaluationOrderId}
                </if>
                <if test="serialNumber != null and serialNumber != ''">
                    and t.serial_number  = #{serialNumber}
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productionBatch != null and productionBatch != ''">
                    and t.production_batch  like concat('%', #{productionBatch} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="quantity != null and quantity != ''">
                    and t.quantity  = #{quantity}
                </if>

                <if test="taskLevel != null and taskLevel != ''">
                    and t.task_level  = #{taskLevel}
                </if>
                <if test="testType != null and testType != ''">
                    and t.test_type  = #{testType}
                </if>
                <if test="workOrderInspectionNumber != null and workOrderInspectionNumber != ''">
                    and t.work_order_inspection_number  like concat('%', #{workOrderInspectionNumber} ,'%')
                </if>
                <if test="qualityGrade != null and qualityGrade != ''">
                    and t.quality_grade  like concat('%', #{qualityGrade} ,'%')
                </if>
                <if test="sampleTotalCount != null and sampleTotalCount != ''">
                    and t.sample_total_count  = #{sampleTotalCount}
                </if>
                <if test="experimentProject != null and experimentProject != ''">
                    and t.experiment_project  like concat('%', #{experimentProject} ,'%')
                </if>
                <if test="specialAnalysisTestProject != null and specialAnalysisTestProject != ''">
                    and t.special_analysis_test_project REGEXP #{specialAnalysisTestProject}
                </if>
                <if test="groupType != null and groupType != ''">
                    and t.group_type REGEXP #{groupType}
                </if>
                <if test="inspectionTestProject != null and inspectionTestProject != ''">
                    and t.inspection_test_project REGEXP #{inspectionTestProject}
                </if>
                <if test="qualityConsistencyTestItems != null and qualityConsistencyTestItems != ''">
                    and t.quality_consistency_test_items REGEXP #{qualityConsistencyTestItems}
                </if>
                <if test="dpaTestProject != null and dpaTestProject != ''">
                    and t.dpa_test_project REGEXP #{dpaTestProject}
                </if>
                <if test="otherTests != null and otherTests != ''">
                    and t.other_tests  like concat('%', #{otherTests} ,'%')
                </if>
                <if test="technicalLiaisonName != null and technicalLiaisonName != ''">
                    and t.technical_liaison_name  like concat('%', #{technicalLiaisonName} ,'%')
                </if>
                <if test="technicalLiaisonPhoneNumber != null and technicalLiaisonPhoneNumber != ''">
                    and t.technical_liaison_phone_number  like concat('%', #{technicalLiaisonPhoneNumber} ,'%')
                </if>
                <if test="deadline != null">
                    and t.deadline  = #{deadline}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="rejectUnscreenableReason != null and rejectUnscreenableReason != ''">
                    and t.reject_unscreenable_reason  like concat('%', #{rejectUnscreenableReason} ,'%')
                </if>

                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
            GROUP BY t.id
	</select>
     <select id="selectOptionsByStandardSpecificationNumber" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.specification_number label,
        	t.id value
        from standard_specification t
        WHERE t.specification_number != '' and t.codex_torch_deleted = '0'
     </select>

    <select id="selectProductList1List" parameterType="com.huatek.frame.modules.business.service.dto.ProductListDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductListVO">
		select
		<include refid="Base_Column_List" />
			from product_list t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="serialNumber != null and serialNumber != ''">
                    and t.serial_number  = #{serialNumber}
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productionBatch != null and productionBatch != ''">
                    and t.production_batch  like concat('%', #{productionBatch} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="quantity != null and quantity != ''">
                    and t.quantity  = #{quantity}
                </if>
                <if test="standardSpecificationNumber0 != null and standardSpecificationNumber0 != ''">
                    and t.standard_specification_number0 REGEXP #{standardSpecificationNumber0}
                </if>
                <if test="taskLevel != null and taskLevel != ''">
                    and t.task_level  = #{taskLevel}
                </if>
                <if test="testType != null and testType != ''">
                    and t.test_type  = #{testType}
                </if>
                <if test="workOrderInspectionNumber != null and workOrderInspectionNumber != ''">
                    and t.work_order_inspection_number  like concat('%', #{workOrderInspectionNumber} ,'%')
                </if>
                <if test="qualityGrade != null and qualityGrade != ''">
                    and t.quality_grade  like concat('%', #{qualityGrade} ,'%')
                </if>
                <if test="sampleTotalCount != null and sampleTotalCount != ''">
                    and t.sample_total_count  = #{sampleTotalCount}
                </if>
                <if test="experimentProject != null and experimentProject != ''">
                    and t.experiment_project  like concat('%', #{experimentProject} ,'%')
                </if>
                <if test="specialAnalysisTestProject != null and specialAnalysisTestProject != ''">
                    and t.special_analysis_test_project REGEXP #{specialAnalysisTestProject}
                </if>
                <if test="groupType != null and groupType != ''">
                    and t.group_type REGEXP #{groupType}
                </if>
                <if test="inspectionTestProject != null and inspectionTestProject != ''">
                    and t.inspection_test_project REGEXP #{inspectionTestProject}
                </if>
                <if test="qualityConsistencyTestItems != null and qualityConsistencyTestItems != ''">
                    and t.quality_consistency_test_items REGEXP #{qualityConsistencyTestItems}
                </if>
                <if test="dpaTestProject != null and dpaTestProject != ''">
                    and t.dpa_test_project REGEXP #{dpaTestProject}
                </if>
                <if test="otherTests != null and otherTests != ''">
                    and t.other_tests  like concat('%', #{otherTests} ,'%')
                </if>
                <if test="technicalLiaisonName != null and technicalLiaisonName != ''">
                    and t.technical_liaison_name  like concat('%', #{technicalLiaisonName} ,'%')
                </if>
                <if test="technicalLiaisonPhoneNumber != null and technicalLiaisonPhoneNumber != ''">
                    and t.technical_liaison_phone_number  like concat('%', #{technicalLiaisonPhoneNumber} ,'%')
                </if>
                <if test="deadline != null">
                    and t.deadline  = #{deadline}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="rejectUnscreenableReason != null and rejectUnscreenableReason != ''">
                    and t.reject_unscreenable_reason  like concat('%', #{rejectUnscreenableReason} ,'%')
                </if>

                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectProductListListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductListVO">
		select
		<include refid="Base_Column_List" />, c.entrusted_unit as entrustedUnit, s.specification_number as specificationNumber, e.order_number as orderNumber
			from product_list t
        LEFT JOIN evaluation_order e ON t.evaluation_order_id = e.id
        LEFT JOIN standard_specification s ON t.standard_specification_id = s.id
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
    <select id="selectProductListsByEvaluationOrderId"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductListVO">
        select
        <include refid="Base_Column_List" />
        from product_list t
        where t.evaluation_order_id = #{evaluationOrderId}
    </select>
<!--    <select id="findProductListById" resultType="com.huatek.frame.modules.business.domain.vo.ProductListVO">-->
<!--        select-->
<!--        <include refid="Base_Column_List"/>, s.specification_number as specificationNumber-->
<!--        from product_list t-->
<!--        LEFT JOIN standard_specification s ON s.id = t.standard_specification_id-->
<!--        where t.id = #{id}-->
<!--    </select>-->
    <select id="findProductListById" resultType="com.huatek.frame.modules.business.domain.vo.ProductListVO">
        SELECT
        <include refid="Base_Column_List"/>,
        GROUP_CONCAT(s.specification_number) as specificationNumber,
        c.entrusted_unit as entrustedUnit,
        c.settlement_unit as settlementUnit,
        c.customer_manager as customerManager
        FROM product_list t
        LEFT JOIN standard_specification s
        ON FIND_IN_SET(s.id, t.standard_specification_id) > 0 OR s.id = t.standard_specification_id
        LEFT JOIN evaluation_order e ON e.id = t.evaluation_order_id
        LEFT JOIN customer_information_management c ON e.customer_id = c.id
        WHERE t.id = #{id}
        GROUP BY t.id
    </select>
</mapper>